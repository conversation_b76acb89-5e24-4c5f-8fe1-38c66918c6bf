<?php

return [
    'remember_me'      => 'Remember me',
    'login'            => 'Login',
    'logout'           => 'Logout',
    'username'         => 'Username',
    'password'         => 'Password',
    'old_password'     => 'Old Password',
    'confirm_password' => 'Confirm Password',
    'captcha'          => 'Captcha',
    'captcha_error'    => 'Captcha Error',
    'required'         => ':attribute is required',
    'login_successful' => 'Login Success',
    'login_failed'     => 'Username Or Password Error',
    'user_setting'     => 'User Settings',
    'created_at'       => 'Created At',
    'updated_at'       => 'Updated At',
    'deleted_at'       => 'Deleted At',
    'actions'          => 'Actions',
    'create'           => 'Create',
    'edit'             => 'Edit',
    'show'             => 'Show',
    'delete'           => 'Delete',
    'batch_delete'     => 'Batch Delete',
    'copy'             => 'Copy',
    'confirm_delete'   => 'Confirm Delete Selected Items?',
    'back'             => 'Back',
    'reset'            => 'Reset',
    'search'           => 'Search',
    'list'             => 'List',
    'add'              => 'Add',
    'save'             => 'Save',
    'detail'           => 'Detail',

    'developer'             => 'Developer',
    'code_generator'        => 'Code Generator',
    'visual_editor'         => 'Visual Editor',
    'terminal'              => 'Terminal',
    'administrator'         => 'Administrator',
    'soft_delete'           => 'Soft Delete',
    'keyword'               => 'Keyword',
    'unknown_error'         => 'Unknown Error',
    'upload_file_error'     => 'Upload File Error',
    'parent'                => 'Parent',
    'order'                 => 'Order',
    'order_desc'            => 'Order Desc',
    'order_asc'             => 'Order Asc',
    'menus'                 => 'Menus',
    'successfully'          => 'Successfully',
    'failed'                => 'Failed',
    'successfully_message'  => ':attribute Successfully',
    'failed_message'        => ':attribute Failed',
    'action_success'        => 'Action Success',
    'action_failed'         => 'Action Failed',
    'save_success'          => 'Save Success',
    'save_failed'           => 'Save Failed',
    'yes'                   => 'Yes',
    'no'                    => 'No',
    'need_start_with_slash' => 'Need Start With /',
    'cancel'                => 'Cancel',
    'please_login'          => 'Please Login',
    'unauthorized'          => 'Unauthorized',
    'user_disabled'         => 'The user has been disabled',
    'preview'               => 'Preview',
    'more'                  => 'More',
    'per_page_suffix'       => 'items/page',

    'code_generators' => [
        'remark1'                     => 'For more parameters, please refer to',
        'remark2'                     => 'Database Migration',
        'remark3'                     => 'Multiple parameters are separated by English commas',
        'table_name'                  => 'Table Name',
        'new_table_name'              => 'New Table Name',
        'model_name'                  => 'Model',
        'controller_name'             => 'Controller',
        'service_name'                => 'Service',
        'primary_key'                 => 'Primary Key',
        'primary_key_description'     => 'Use increments method',
        'options'                     => 'Options',
        'create_database_migration'   => 'Create Database Migration File',
        'create_table'                => 'Create Table',
        'create_model'                => 'Create Model',
        'create_controller'           => 'Create Controller',
        'create_service'              => 'Create Service',
        'app_title'                   => 'App Title',
        'new_app_title'               => 'New App Title',
        'column_name'                 => 'Column Name',
        'type'                        => 'Type',
        'extra_params'                => 'Extra Params',
        'nullable'                    => 'Nullable',
        'index'                       => 'Index',
        'default_value'               => 'Default Value',
        'comment'                     => 'Comment',
        'exists_table'                => 'Exists Table',
        'generate_code'               => 'Generate Code',
        'expand_more_settings'        => 'More Settings',
        'collapse_settings'           => 'Collapse Settings',
        'confirm_generate_code'       => 'Confirm Generate Code?',
        'preview'                     => 'Preview',
        'base_info'                   => 'Base Info',
        'column_info'                 => 'Column Info',
        'route_config'                => 'Route Config',
        'page_config'                 => 'Page Config',
        'gen_route_menu'              => 'Generate Route And Menu',
        'route'                       => 'Route',
        'menu_name'                   => 'Menu Name',
        'parent_menu'                 => 'Parent Menu',
        'menu_icon'                   => 'Menu Icon',
        'name_label_desc'             => 'Name And Label Attribute Take Field Name And Comment',
        'column_warning'              => 'If the field name exists (no / status) will cause the form to fail to echo back!',
        'add_column'                  => 'Add Column',
        'scope'                       => 'Scope',
        'list_component'              => 'List Component',
        'list_component_desc'         => 'List Component, Default Is TableColumn',
        'form_component'              => 'Form Component',
        'form_component_desc'         => 'Form Component, Default Is TextControl',
        'detail_component'            => 'Detail Component',
        'detail_component_desc'       => 'Detail Component, Default Is TextControl',
        'model_config'                => 'Model Config',
        'file_column'                 => 'File Column',
        'file_column_multi'           => 'Multi File',
        'file_column_desc'            => 'File Column, Automatically Add Attribute Methods In The Model',
        'preview_code'                => 'Preview Code',
        'property'                    => 'Property',
        'property_name'               => 'Property Name',
        'value'                       => 'Value',
        'dialog_form'                 => 'Dialog Form',
        'copy_record'                 => 'Copy Record',
        'copy_record_description'     => 'You can copy and share to <a href="https://github.com/Slowlyo/owl-admin/discussions/categories/%E4%BB%A3%E7%A0%81%E7%94%9F%E6%88%90%E8%AE%B0%E5%BD%95" target="_blank">Github</a>',
        'import_record'               => 'Import Record',
        'import_record_placeholder'   => 'Please enter the imported json record',
        'import_record_desc'          => 'You can find some records shared by others on <a href="https://github.com/Slowlyo/owl-admin/discussions/categories/%E4%BB%A3%E7%A0%81%E7%94%9F%E6%88%90%E8%AE%B0%E5%BD%95" target="_blank">Github</a>',
        'load_config'                 => 'Load Config',
        'load_component_config'       => 'Load :c Config',
        'fill'                        => 'Fill',
        'save_current_config'         => 'Save Current Config',
        'input_config_name'           => 'Please Input Config Name',
        'same_name_tips'              => 'Configurations with the same name will be overwritten',
        'save_path_dir'               => 'Primary application',
        'save_path_select'            => 'Toggle Directory',
        'save_path_select_tips'       => 'Project root directory or plugin root directory can be selected',
        'save_path_label_prefix'      => 'Plugins -> ',
        'clear_code'                  => 'Clear Code',
        'select_clear_record'         => 'Select what you want to clear',
        'select_generate_record'      => 'Select the content you want to generate',
        'duplicate_column'            => 'Duplicate column name',
        'common_field_add'            => 'Add Common Field',
        'common_field_add_column'     => 'Add Field',
        'config_name'                 => 'Config Name',
        'field_config'                => 'Field Config',
        'field_name'                  => 'Field Name',
        'list_display'                => 'List Display :content',
        'duplicate_filter_input_name' => 'Duplicate filter input name: :column',
        'list_filter'                 => 'List Filter',
        'filter_type'                 => 'Filter Type',
        'filter_mode'                 => 'Filter Mode',
        'filter_mode_fixed'           => 'Fixed Query Condition',
        'filter_mode_fixed_value'     => 'Fixed Value',
        'filter_mode_input'           => 'Receive Parameter',
        'filter_input_name'           => 'Parameter Name',
        'filter_input_label'          => 'Filter Label',
        'filter_component'            => 'Filter Component',
        'page'                        => 'Page',
        'dialog'                      => 'Dialog',
        'drawer'                      => 'Drawer',
        'dialog_size'                 => 'Dialog Size',
        'drawer_size'                 => 'Drawer Size',
        'clone_record'                => 'Clone Record',
    ],

    'admin_users' => 'Admin Users',
    'admin_user'  => [
        'avatar'                  => 'Avatar',
        'name'                    => 'Name',
        'roles'                   => 'Roles',
        'search_username'         => 'Search Username/Name',
        'password_confirmation'   => 'Password Not Match',
        'old_password_required'   => 'Old Password Required',
        'old_password_error'      => 'Old Password Error',
        'username_already_exists' => 'Username Already Exists',
        'cannot_delete'           => 'The super administrator cannot be deleted',
    ],

    'admin_roles' => 'Admin Roles',
    'admin_role'  => [
        'name'                => 'Name',
        'slug'                => 'Slug',
        'permissions'         => 'Permissions',
        'slug_description'    => 'unique identifier of a role',
        'name_already_exists' => 'Role Name Already Exists',
        'slug_already_exists' => 'Role Slug Already Exists',
        'set_permissions'     => 'Set Permissions',
        'cannot_delete'       => 'The super administrator cannot be deleted',
        'used'                => 'You cannot delete a role that is in use',
    ],

    'admin_permissions' => 'Permissions',
    'admin_permission'  => [
        'name'                    => 'Name',
        'slug'                    => 'Slug',
        'http_method'             => 'Http Method',
        'http_method_description' => 'if you do not select the value is ANY',
        'http_path'               => 'Http Path',
        'auto_generate'           => 'Auto Generate',
        'auto_generate_confirm'   => 'The permission information will be generated after the permission table and permission menu association table are truncated. Do you want to continue?',
        'parent_id_not_allow'     => 'Parent Id Not Allow',
        'name_already_exists'     => 'Permission Name Already Exists',
        'slug_already_exists'     => 'Permission Slug Already Exists',
    ],

    'admin_menus' => 'Menus',
    'admin_menu'  => [
        'parent_id'              => 'Parent',
        'order'                  => 'Order',
        'title'                  => 'Title',
        'icon'                   => 'Icon',
        'icon_description'       => 'Please refer to ',
        'url'                    => 'Url',
        'iframe_description'     => 'After opening, the page will be cached and will not be reloaded when reopened',
        'visible'                => 'Visible',
        'type'                   => 'Type',
        'keep_alive'             => 'KeepAlive',
        'api'                    => 'Page API',
        'api_description'        => 'schemaApi, the api for page initialization requests, needs to be consistent with queryPath in Controller',
        'route'                  => 'Route',
        'link'                   => 'Link',
        'iframe'                 => 'Iframe',
        'page'                   => 'Page',
        'class_name'             => 'Class Name',
        'class_name_description' => 'The CSS class name of the menu, which is typically used to customize styles',
        'show'                   => 'Show',
        'hide'                   => 'Hide',
        'is_home'                => 'Is Home',
        'is_home_description'    => 'In multi-tab mode, the page label is fixed on the left',
        'is_full'                => 'Is Full',
        'is_full_description'    => 'When enabled, the menu bar portion of the page is hidden',
        'parent_id_not_allow'    => 'The parent menu cannot be set to the current submenu',
        'component'              => 'Component',
        'component_desc'         => 'amis by default. Not a custom front-end page, please do not modify it',
        'url_exists'             => 'menu path duplication',
    ],

    'extensions'    => [
        'name_invalid'       => 'Name Invalid',
        'exists'             => 'This extension already exists: ',
        'menu'               => 'Extensions',
        'page_title'         => 'Extensions',
        'create'             => 'Create',
        'install'            => 'Install',
        'create_extension'   => 'Create Extension',
        'create_tips'        => 'After the directory is created, a basic extended directory structure is created under the <br><b>:dir</b><br> directory',
        'local_install'      => 'Local Install',
        'more_extensions'    => 'More Extensions',
        'setting'            => 'Setting',
        'enable'             => 'Enable',
        'enable_confirm'     => 'Are you sure to enable the extension?',
        'disable'            => 'Disable',
        'disable_confirm'    => 'Are you sure to disable the extension?',
        'uninstall'          => 'Uninstall',
        'uninstall_confirm'  => '
<div class="text-danger">
Confirm to uninstall the extension?<br>
Uninstallation will delete all files and databases published after the extension is enabled and cannot be retrieved!!!<br>
Backup important data before performing operations!!!<br>
<span class="text-info">Extension package files will not be deleted, please manually delete them!!!</span>
</div>
',
        'filter_placeholder' => 'Search by extension name',
        'form'               => [
            'create_extension'   => 'Create Extension',
            'name'               => 'Name',
            'namespace'          => 'Namespace',
            'create_description' => 'It will be created in :dir Directory to create a basic extended directory structure',
        ],
        'card'               => [
            'author'   => 'Author',
            'version'  => 'Version',
            'homepage' => 'Home',
            'status'   => 'Status',
        ],
        'status_map'         => [
            'enabled'  => 'Enabled',
            'disabled' => 'Disabled',
        ],
        'validation'         => [
            'file'            => 'Please select file',
            'invalid_package' => 'Invalid extension package',
        ],
    ],
    'export'        => [
        'title'                        => 'Export',
        'all'                          => 'All',
        'page'                         => 'Current Page',
        'selected_rows'                => 'Selected Rows',
        'page_no_data'                 => 'Current page has no data',
        'selected_rows_no_data'        => 'No data is selected',
        'please_install_laravel_excel' => 'Please install the package "rap2hpoutre/fast-excel" first',
    ],
    'pages'         => [
        'menu'                   => 'Pages',
        'title'                  => 'Title',
        'sign'                   => 'Sign',
        'page'                   => 'Page Structure',
        'schema_cannot_be_empty' => 'Page Structure cannot be empty',
        'sign_exists'            => 'Sign already exists',
    ],
    'relationships' => [
        'menu'            => 'Dynamic Relationships',
        'model'           => 'Model',
        'title'           => 'Title',
        'remark'          => 'Remark',
        'generate_model'  => 'Generate Model',
        'generate'        => 'Generate',
        'args'            => 'Args',
        'type'            => 'Type',
        'related_model'   => 'Related Model',
        'through_model'   => 'Through Model',
        'model_exists'    => 'Model already exists: ',
        'rel_name_exists' => 'Relationship name already exists',
    ],
    'apis'          => [
        'menu'                  => 'Dynamic Apis',
        'title'                 => 'Title',
        'path'                  => 'Path',
        'template'              => 'Template',
        'enabled'               => 'Enabled',
        'args'                  => 'Args',
        'path_exists'           => 'Path already exists',
        'template_format_error' => 'Template format error',
        'template_exists'       => 'Template already exists',
        'add_template'          => 'Add Template',
        'add_template_tips'     => 'Please note the source of the template, if you can not tell whether the template is safe or not, it is recommended not to use a template',
        'paste_template'        => 'Paste the template content',
        'overlay'               => 'Whether to overwrite the template if it already exists',
    ],
    'api_templates' => [
        'data_list'   => 'Get List (Paginated)',
        'data_create' => 'Create',
        'data_update' => 'Update',
        'data_delete' => 'Delete',
        'data_detail' => 'Get Detail',
    ],
];
