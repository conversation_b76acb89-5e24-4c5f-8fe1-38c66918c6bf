.home-product-swiper .swiper-wrapper{
    align-items: center;
}
@media screen and (min-width: 1024px) {
    .home-product-swiper{
        height: 550px !important;
    }
    .home-product-swiper .swiper-slide-active{
        position: relative;
        width: 70% !important;
        margin-left: 0px !important;
    }
    .home-product-swiper .swiper-slide-active::after{
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 80%;
        border-radius: 20px;
        height: 100%;
        z-index:-1;
        background-color: #008891 !important;
    }
    .home-product-swiper .swiper-slide-active .bg-main-color{
        background-color: #ffffff00 !important;
    }
    .home-product-swiper .swiper-slide-next.product-slide,.swiper-slide-prev.product-slide{
        width: calc(15% - 20px) !important;
    }
    .home-product-swiper .swiper-slide-next.product-slide .right,.swiper-slide-prev.product-slide .right{
        width: 0;
        display: none;
        transition: width, padding 0s;
    }
    .home-product-swiper .swiper-slide-next.product-slide .left,.swiper-slide-prev.product-slide .left{
        width: 100%;
    }
    .home-product-swiper .swiper-slide-next.product-slide .left,.swiper-slide-prev.product-slide .left{
        width: 100%;
    }
}
@media screen and (min-width: 768px) {
    .home-product-swiper {
        height: 600px;
    }
    .swiper-slide-active .product-slide-box{
        position: relative;
        flex-direction: row !important;
        background-color: #ffffff00 !important;
    }
    .swiper-slide-active .product-slide-box::after{
        content: '';
        position: absolute;
        top:0;
        right: 0;
        width: 80%;
        height: 100%;
        z-index:-1;
        background-color: #008891 !important;
        /* border-top-left-radius: 20px;
        border-bottom-left-radius: 20px; */
        border-radius: 20px;
    }
    .swiper-slide-active .product-slide-box::before{
        content: '';
        position: absolute;
        bottom:0;
        right: 0;
        width: 80%;
        height: 70%;
        z-index:-1;
        background: url('/public/HomePage/Image_mask.png') no-repeat center center / cover;
        /* border-top-left-radius: 20px;
        border-bottom-left-radius: 20px; */
        border-radius: 20px;
    }
    .swiper-slide-active .product-slide-box .left{
        background-color: #ffffff00 !important;
        padding: 0 !important;
    }
}
@media screen and (max-width:767px){
    .product-slide-box{
        padding:10px;
        flex-direction: column;
        background-color: #008891;
    }
    .product-slide-box .left{
        width: 100%;
    }
    .product-slide-box .right{
        width: 100%;
    }
    .home-product-swiper{
        background-color: #008891;
    }
}
.home-product-swiper .swiper-slide:nth-child(1){
    opacity: 0 !important;
}

.video-wrapper {
    position: relative;
    width: 100%;
    padding-top: 56.25%; /* 16:9 宽高比 */
}

.video-element {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.text-animate{
    animation: text-animate .8s ease-in-out;
}
@keyframes text-animate {
    0%{
        opacity: 0;
        transform: translateX(20px);
    }
    100%{
        opacity: 1;
        transform: translateX(0);
    }
}