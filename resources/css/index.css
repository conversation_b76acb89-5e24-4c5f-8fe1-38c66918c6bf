.layout-container{
    max-width: 1300px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    min-height: 100vh;
}
.main-color{
    color: #008891;
}
.bg-main-color{
    background-color: #008891;
}
.second-color{
    color: #DEDC00;
}
.bg-second-color{
    background-color: #DEDC00;
}
body{
    font-family:poppins;
    min-width: 300px;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}
.container{
    padding: 0 16px;
}
.container-py{
    padding: 30px 16px;
}

.ant-tabs-nav{
    margin-bottom: 0px !important;
}

.ant-tabs-nav-wrap{
    width: 100% !important;
    padding: 1rem !important;
}

.ant-tabs-tab-active .ant-tabs-tab-btn{
    color: #008891 !important;
}

.ant-tabs-ink-bar{
    background-color: #008891 !important;
    background: #008891 !important;
}

.topic-container {
    /* visibility: hidden; */
    will-change: transform;
}

.topic-item {
    opacity: 1;
    transform: translateY(0);
    will-change: transform, opacity;
}

/* GSAP 动画相关样式 */
.topic-item.gsap-init {
    opacity: 1;
    transform: translateY(50px);
}

/* 文本动画相关样式 */
.text-mask-container {
    position: relative;
    overflow: hidden;
    display: inline-block;
    vertical-align: top;
}

.text-mask-content {
    display: inline-block;
    transform: translateY(100%);
    opacity: 0;
    will-change: transform, opacity;
}

.ant-tabs-tab-btn{
    padding: 0px 1px !important;
}
.ant-tabs-nav-wrap{
    padding: 16px 0px !important;
}