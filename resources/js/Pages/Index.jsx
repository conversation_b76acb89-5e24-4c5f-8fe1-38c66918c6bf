import React, { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import Header from '@/Pages/Components/Header/Header';
import { Head } from '@inertiajs/react';
import '../../css/index.css';
import SearchBar from '@/Pages/Components/HomePage/SearchBar';
import HomeBanner from '@/Pages/Components/HomePage/HomeBanner';
import HomeProduct from '@/Pages/Components/HomePage/HomeProduct';
import HomeAbout from '@/Pages/Components/HomePage/HomeAbout';
import HomeGuide from '@/Pages/Components/HomePage/HomeGuide';
import HomeRecommend from '@/Pages/Components/HomePage/HomeRecommend';
import Footer from '@/Pages/Components/Footer/Footer';
import ButtonGroup from '@/Pages/Components/FixedButtonGroup/ButtonGroup';
import { initializeGSAP as initializeGSAPScroll } from './Components/HomePage/HomeGSAPScroll';
import initializeGSAP from './Components/HomePage/HomeGSAP';

const Index = () => {
    useEffect(() => {
        // 初始化两个 GSAP 动画
        initializeGSAPScroll();
        initializeGSAP();

        // 清理函数
        return () => {
            // 如果需要清理动画相关的内容
        };
    }, []); // 空依赖数组，只在组件挂载时执行一次

    return (
        <>
            <Head title={import.meta.env.VITE_APP_NAME + " - " + "主頁"}  />
            <div className='layout-container'>
                <ButtonGroup />
                <Header />
                <SearchBar />
                <HomeBanner />
                <div className="container-py bg-gradient-to-t from-white to-[#F2F3F3] ">
                    <div className="relative w-full">
                        <div className='topic-container text-center md:py-20 py-5'>
                            <div className='t1 text-3xl md:text-5xl font-bold main-color'>產品類別</div>
                            <div className='t2 text-lg md:text-xl mt-2 md:mt-4 main-color text-transform: uppercase'>Treasure Classification</div>
                        </div>
                        <HomeProduct />
                    </div>
                </div>
                <HomeAbout />
                <HomeGuide />
                <div className="mt-[-15px] container-py bg-gradient-to-t from-white to-[#F2F3F3] ">
                    <div className='topic-container text-center md:py-20 py-5'>
                        <div className='t1 text-3xl md:text-5xl font-bold main-color'>熱門推薦</div>
                        <div className='t2 text-lg md:text-xl mt-2 md:mt-4 main-color text-transform: uppercase'>Hot Recommendation</div>
                    </div>
                    <HomeRecommend />
                </div>
                <Footer />
            </div>
        </>
    );
};

export default Index;
