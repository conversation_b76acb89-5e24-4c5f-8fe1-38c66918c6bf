import { Head } from '@inertiajs/react';
import { message } from 'antd';
import Header from '@/Pages/Components/Header/Header';
import ButtonGroup from '@/Pages/Components/FixedButtonGroup/ButtonGroup';
import RedPocketImageCropper from './Components/RedPocketImageCropper/RedPocketImageCropper';
import Footer from '@/Pages/Components/Footer/Footer';
import { PDFDocument } from 'pdf-lib';
import axios from 'axios';
import { router } from '@inertiajs/react';
import { useState, useEffect, useRef } from 'react';


const ProductRedPocketEdit = ({ auth, ClientProject, product }) => {
    const [croppedImageData, setCroppedImageData] = useState(null);
    const [selectedImage, setSelectedImage] = useState(null);
    const [previewUrl, setPreviewUrl] = useState(null);
    const [key, setKey] = useState(0);
    const [currentPattern, setCurrentPattern] = useState(1);
    const [messageApi, contextHolder] = message.useMessage();
    const [notification, setNotification] = useState(null);
    const containerRef = useRef(null);
    const [imageUrl, setImageUrl] = useState(null);

    useEffect(() => {
        if (auth.user?.delivery_info) {
            setDeliveryInfo(auth.user.delivery_info);
        }
    }, [auth.user]);

    useEffect(() => {
        const loadExistingProject = async () => {
            if (ClientProject && ClientProject.project_data) {                
                try {
                    const projectData = ClientProject.project_data;
                    
                    // 设置图案
                    setCurrentPattern(ClientProject.product_template_id);
                    
                    // 加载保存的图片
                    if (projectData.image && projectData.image.stored_url) {
                        const img = new Image();
                        img.crossOrigin = "anonymous";
                        
                        img.onload = () => {
                            // 创建 canvas
                            const canvas = document.createElement('canvas');
                            canvas.width = projectData.canvas.width;
                            canvas.height = projectData.canvas.height;
                            const ctx = canvas.getContext('2d');
                            
                            // 设置图片位置和变换
                            ctx.save();
                            ctx.translate(projectData.image.left, projectData.image.top);
                            ctx.rotate(projectData.image.rotation || 0);
                            ctx.scale(
                                projectData.image.scaleX || 1,
                                projectData.image.scaleY || 1
                            );
                            ctx.drawImage(img, 0, 0);
                            ctx.restore();

                            // 设置裁剪数据
                            setCroppedImageData({
                                canvas,
                                image: {
                                    ...projectData.image,
                                    url: projectData.image.stored_url,
                                    // 保存原始图片对象以供后续使用
                                    element: img
                                }
                            });
                            setSelectedImage(true);
                        };                        
                        img.onerror = (error) => {
                            console.error('加載圖片失敗:', error);
                            messageApi.error('加載保存的圖片失敗');
                        };

                        img.src = projectData.image.stored_url;
                    }
                } catch (error) {
                    console.error('加載保存的設計失敗:', error);
                    messageApi.error('加載保存的設計失敗');
                }
            }
        };

        loadExistingProject();
    }, [ClientProject]);

    useEffect(() => {
        if (notification) {
            messageApi.open({
                type: 'success',
                content: notification,
                duration: 3,
            });
            setNotification(null);
        }
    }, [notification, messageApi]);

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const imageParam = urlParams.get('image');
        if (imageParam) {
            console.log(`Image URL found: ${imageParam}`); // Debug log
            setImageUrl(imageParam);
        } else {
            console.log('No image URL found in query parameters.');
        }
    }, []);


    const handleCropComplete = (cropData) => {
        setCroppedImageData(cropData);
        setSelectedImage(true);
    };

    const handleSubmit = async (event) => {
        try {
            if (!croppedImageData || !croppedImageData.canvas) {
                messageApi.error('請先上傳並裁剪圖片');
                return;
            }

            // 创建 FormData
            const formData = new FormData();
            
            // 从 canvas 元素获取图片数据
            const canvasElement = croppedImageData.canvas;
            const imageDataUrl = canvasElement.toDataURL('image/png', 1.0);
            
            // 将 base64 转换为 Blob
            const fetchResponse = await fetch(imageDataUrl);
            const blob = await fetchResponse.blob();
            
            // 创建文件对象
            const imageFile = new File([blob], 'design.png', { type: 'image/png' });
            
            // 添加图片文件
            formData.append('image', imageFile);
            
            // 生成 PDF 并添加到表单
            const pdfFile = await handlePDFDownload();
            messageApi.success('設計保存成功');
        } catch (error) {
            console.error('提交表單時出錯:', error);
            messageApi.error('提交失敗，請稍後重試');
        }
    };

    const handlePDFDownload = async () => {
        if (!croppedImageData) return null;

        try {
            // 加载 PDF 模板
            let response;
            let retries = 3;
            while (retries > 0) {
                try {
                    response = await fetch(`/PacketDesign/red-pocket-pattern${currentPattern}.pdf`);
                    if (response.ok) break;
                } catch (err) {
                    console.warn(`PDF 加載重試剩餘 ${retries - 1} 次`);
                }
                retries--;
                if (retries === 0) {
                    throw new Error('PDF 模板加載失敗，請稍後再試');
                }
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            const pdfBytes = await response.arrayBuffer();
            const pdfDoc = await PDFDocument.load(pdfBytes);
            
            const { canvas } = croppedImageData;
            
            // 获取高质量的图片数据
            const croppedDataURL = canvas.toDataURL({
                format: 'png',
                quality: 1,
                multiplier: 4,
                enableRetinaScaling: true
            });
            
            // 加载裁剪后的图片
            const croppedImage = new Image();
            await new Promise((resolve, reject) => {
                croppedImage.onload = resolve;
                croppedImage.onerror = reject;
                croppedImage.src = croppedDataURL;
            });
            console.log(croppedImage);
            
            // 获取原始尺寸
            const originalWidth = croppedImage.naturalWidth;
            const originalHeight = croppedImage.naturalHeight;
            
            // 创建两个临时画布
            const tempCanvas1 = document.createElement('canvas');
            const tempCanvas2 = document.createElement('canvas');
            
            tempCanvas1.width = originalWidth;
            tempCanvas1.height = originalHeight;
            tempCanvas2.width = originalWidth;
            tempCanvas2.height = originalHeight;
            
            // 获取上下文并设置图像平滑
            const ctx1 = tempCanvas1.getContext('2d');
            const ctx2 = tempCanvas2.getContext('2d');
            
            ctx1.imageSmoothingEnabled = true;
            ctx1.imageSmoothingQuality = 'high';
            ctx2.imageSmoothingEnabled = true;
            ctx2.imageSmoothingQuality = 'high';
            
            // 绘制原始图片到临时画布
            ctx1.drawImage(croppedImage, 0, 0, originalWidth, originalHeight);
            ctx2.drawImage(croppedImage, 0, 0, originalWidth, originalHeight);
            
            // 创建旋转后的画布 - 图片1
            const angle1 = 321.3 * Math.PI / 180;
            const cos1 = Math.abs(Math.cos(angle1));
            const sin1 = Math.abs(Math.sin(angle1));
            const rotatedWidth1 = originalWidth * cos1 + originalHeight * sin1;
            const rotatedHeight1 = originalWidth * sin1 + originalHeight * cos1;
            
            const rotatedCanvas1 = document.createElement('canvas');
            rotatedCanvas1.width = rotatedWidth1;
            rotatedCanvas1.height = rotatedHeight1;
            const rotatedCtx1 = rotatedCanvas1.getContext('2d');
            
            rotatedCtx1.save();
            rotatedCtx1.translate(rotatedWidth1/2, rotatedHeight1/2);
            rotatedCtx1.rotate(angle1);
            rotatedCtx1.drawImage(tempCanvas1, -originalWidth/2, -originalHeight/2);
            rotatedCtx1.restore();
            
            // 创建旋转后的画布 - 图片2
            const angle2 = 140.7 * Math.PI / 180;
            const cos2 = Math.abs(Math.cos(angle2));
            const sin2 = Math.abs(Math.sin(angle2));
            const rotatedWidth2 = originalWidth * cos2 + originalHeight * sin2;
            const rotatedHeight2 = originalWidth * sin2 + originalHeight * cos2;
            
            const rotatedCanvas2 = document.createElement('canvas');
            rotatedCanvas2.width = rotatedWidth2;
            rotatedCanvas2.height = rotatedHeight2;
            const rotatedCtx2 = rotatedCanvas2.getContext('2d');
            
            rotatedCtx2.save();
            rotatedCtx2.translate(rotatedWidth2/2, rotatedHeight2/2);
            rotatedCtx2.rotate(angle2);
            rotatedCtx2.drawImage(tempCanvas2, -originalWidth/2, -originalHeight/2);
            rotatedCtx2.restore();
            
            // 将旋转后的图片嵌入到 PDF
            const [pngImage1, pngImage2] = await Promise.all([
                pdfDoc.embedPng(rotatedCanvas1.toDataURL('image/png')),
                pdfDoc.embedPng(rotatedCanvas2.toDataURL('image/png'))
            ]);
            
            // 获取页面
            const page = pdfDoc.getPages()[0];
            
            // 设置缩放因子
            const scaleFactor1 = Math.min(930 / rotatedWidth1, 475 / rotatedHeight1);
            const scaleFactor2 = Math.min(930 / rotatedWidth2, 475 / rotatedHeight2);
            
            // 绘制图片到 PDF
            page.drawImage(pngImage1, {
                x: 147,
                y: 726,
                width: rotatedWidth1 * scaleFactor1,
                height: rotatedHeight1 * scaleFactor1
            });
            
            page.drawImage(pngImage2, {
                x: 187,
                y: 119,
                width: rotatedWidth2 * scaleFactor2,
                height: rotatedHeight2 * scaleFactor2
            });

            // 保存 PDF
            const modifiedPdfBytes = await pdfDoc.save();
            // download PDF
            const blob = new Blob([modifiedPdfBytes], { type: 'application/pdf' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'design.pdf';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('生成 PDF 時出錯:', error);
            messageApi.error('生成 PDF 失敗: ' + error.message);
            return null;
        }
    };

    const handlePatternChange = (patternNumber) => {
        setCurrentPattern(patternNumber);
    };

    return (
        <>
            <Head title={import.meta.env.VITE_APP_NAME + " - " + "產品詳情"} />
            <div className='layout-container'>
                {contextHolder}
                {/* <ButtonGroup /> */}
                {/* <Header /> */}
                <div className="mx-auto p-4">
                    <div className="max-w-2xl mx-auto">
                    <div className="md:text-2xl text-lg font-bold mb-4 underline underline-offset-4">利是設計</div>
                        <div className="mb-4 relative pocket-container" ref={containerRef}>
                            <img 
                                src={`/PacketDesign/red-pocket-pattern${currentPattern}.png`}
                                alt="背景圖片" 
                                className="w-full h-full object-cover relative" 
                            />
                            <RedPocketImageCropper
                                style={{
                                    position: "absolute",
                                    top: 0,
                                    left: 0,
                                    width: "100%",
                                    height: "100%"
                                }}
                                imageUrl={imageUrl}
                                backgroundImage={""}
                                onCropComplete={handleCropComplete}
                                key={key}
                                containerRef={containerRef}
                            />
                        </div>
                        <div className="red-pocket-pattern flex flex-wrap gap-2">
                            {[1, 2, 3, 4, 5].map((number) => (
                                <button
                                    key={number}
                                    onClick={() => handlePatternChange(number)}
                                    className={`red-pocket-pattern-item w-20 h-auto p-2 rounded-lg transition-all duration-200
                                        ${currentPattern === number 
                                            ? 'bg-second-color bg-opacity-20 ring-2 ring-second-color' 
                                            : 'bg-gray-100 hover:bg-gray-200'
                                        }`}
                                >
                                    <img 
                                        src={`/PacketDesign/red-pocket-pattern${number}.png`} 
                                        alt={`紅包圖案 ${number}`}
                                        className="w-full h-auto"
                                    />
                                </button>
                            ))}
                        </div>
                        <div className="mt-4 space-y-4">
                            <div className="flex justify-start space-x-4">
                                {/* <button 
                                    className="px-4 py-2 bg-gray-200 rounded-lg hover:bg-gray-300"
                                    onClick={handleCancel}
                                >
                                    重置圖片
                                </button> */}
                                <button     
                                    className={`px-4 py-2 rounded-lg ${
                                        !selectedImage
                                            ? 'bg-gray-300 cursor-not-allowed'
                                            : 'bg-second-color text-white hover:bg-opacity-90'
                                    }`}
                                    onClick={handleSubmit}
                                    disabled={!selectedImage}
                                >
                                    保存
                                    {/* {!deliveryInfo ? '請先添加配送資訊' : '保存並下載'} */}
                                </button>
                            </div>
                        </div>
                        {/* <div className="mt-4">
                            <div className="p-4 bg-white rounded-lg shadow">
                                <h3 className="text-lg font-semibold mb-4">配送方式</h3>
                                <div className="space-y-3">
                                    {deliveryOptions.map((option) => (
                                        <div
                                            key={option.value}
                                            className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                                                deliveryMethod === option.value
                                                    ? 'border-second-color bg-second-color bg-opacity-10'
                                                    : 'border-gray-200 hover:border-gray-300'
                                            }`}
                                            onClick={() => setDeliveryMethod(option.value)}
                                        >
                                            <div className="flex items-center gap-3">
                                                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                                                    deliveryMethod === option.value
                                                        ? 'border-second-color'
                                                        : 'border-gray-400'
                                                }`}>
                                                    {deliveryMethod === option.value && (
                                                        <div className="w-2 h-2 rounded-full bg-second-color" />
                                                    )}
                                                </div>
                                                <div>
                                                    <div className="font-medium">{option.label}</div>
                                                    <div className="text-sm text-gray-500">{option.description}</div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                        {deliveryMethod === 'delivery' && (
                            <>
                                <div className="mt-4">
                                    <div className="p-4 bg-white rounded-lg shadow">
                                        <h3 className="text-lg font-semibold mb-4">配送資訊</h3>
                                        <DeliveryInfoForm 
                                            onSuccess={(values) => {
                                                setDeliveryInfo(values);
                                            }}
                                            initialValues={deliveryInfo}
                                            userAddresses={userAddresses}
                                        />
                                    </div>
                                </div>

                                {deliveryInfo && (
                                    <div className="mt-4">
                                        <DeliveryInfoDisplay 
                                            deliveryInfo={deliveryInfo}
                                            showEditButton={false}
                                        />
                                    </div>
                                )}
                            </>
                        )}
                        <div className="mt-4">
                            <button 
                                className={`w-full px-4 py-2 rounded-lg ${
                                    !selectedImage || (deliveryMethod === 'delivery' && !deliveryInfo)
                                        ? 'bg-gray-300 cursor-not-allowed'
                                        : 'bg-second-color text-white hover:bg-opacity-90'
                                }`}
                                onClick={handleSave}
                                disabled={!selectedImage || (deliveryMethod === 'delivery' && !deliveryInfo)}
                            >
                                {!selectedImage 
                                    ? '請先上傳圖片'
                                    : deliveryMethod === 'delivery' && !deliveryInfo 
                                        ? '請填寫配送資訊'
                                        : `保存並下載 ${quantity} 份`}
                            </button>
                        </div> */}
                    </div>
                </div>
                {/* <Footer /> */}
            </div>
        </>
    );
};

export default ProductRedPocketEdit; 