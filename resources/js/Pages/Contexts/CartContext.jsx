import React, { createContext, useContext, useState, useEffect } from 'react';

const CartContext = createContext();

export function CartProvider({ children, user = null }) {
    const [cartItems, setCartItems] = useState([]);
    const [totalPrice, setTotalPrice] = useState(0);

    // 从 localStorage 加载购物车数据
    useEffect(() => {
        if (user?.id) {
            const storedCart = localStorage.getItem(`cart_${user.id}`);
            if (storedCart) {
                try {
                    const parsedCart = JSON.parse(storedCart);
                    setCartItems(parsedCart);
                    calculateTotal(parsedCart);
                } catch (error) {
                    console.error('Failed to parse cart data:', error);
                    localStorage.removeItem(`cart_${user.id}`);
                }
            }
        } else {
            setCartItems([]);
            setTotalPrice(0);
        }
    }, [user]);

    // 保存到 localStorage
    useEffect(() => {
        if (user?.id) {
            localStorage.setItem(`cart_${user.id}`, JSON.stringify(cartItems));
            calculateTotal(cartItems);
        }
    }, [cartItems, user]);

    // 计算总价
    const calculateTotal = (items) => {
        const total = items.reduce((sum, item) => sum + (item.price * item.count), 0);
        setTotalPrice(total);
    };

    // 添加到购物车
    const addToCart = (product, count = 1) => {
        if (!user?.id) {
            return false; // 未登录用户不能添加商品
        }

        setCartItems(prevItems => {
            const existingItem = prevItems.find(item => 
                item.id === product.id && item.uuid === product.uuid
            );

            if (existingItem) {
                return prevItems.map(item =>
                    item.id === product.id && item.uuid === product.uuid
                        ? { ...item, count: item.count + count }
                        : item
                );
            }

            return [...prevItems, { ...product, count }];
        });
    };

    // 从购物车移除
    const removeFromCart = (id, uuid) => {
        if (!user?.id) return;
        setCartItems(prevItems => 
            prevItems.filter(item => !(item.id === id && item.uuid === uuid))
        );
    };

    // 更新数量
    const updateQuantity = (id, uuid, count) => {
        if (!user?.id) return;
        setCartItems(prevItems =>
            prevItems.map(item =>
                item.id === id && item.uuid === uuid
                    ? { ...item, count }
                    : item
            )
        );
    };

    // 清空购物车
    const clearCart = () => {
        if (!user?.id) return;
        setCartItems([]);
    };

    return (
        <CartContext.Provider value={{
            cartItems,
            setCartItems,
            totalPrice,
            addToCart,
            removeFromCart,
            updateQuantity,
            clearCart
        }}>
            {children}
        </CartContext.Provider>
    );
}

export function useCart() {
    const context = useContext(CartContext);
    if (!context) {
        throw new Error('useCart must be used within a CartProvider');
    }
    return context;
} 