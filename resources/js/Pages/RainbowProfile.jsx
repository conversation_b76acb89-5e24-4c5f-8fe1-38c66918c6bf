import React, { useState } from 'react';
import Marquee from "react-marquee-slider";
import times from "lodash/times";

export default function RainboxLogin() {
  const [velocity, setVelocity] = useState(20);
  const [categoryIndex, setCategoryIndex] = useState(0);

  const imgList = [
    {
      category: "2025",
      img: [
        {
          src: "/Icon/Rainbow/DisplaySample/img1.jpeg",
          alt: "img1"
        },
        {
          src: "/Icon/Rainbow/DisplaySample/img2.jpeg",
          alt: "img2"
        },
        {
          src: "/Icon/Rainbow/DisplaySample/img3.jpeg",
          alt: "img3"
        }
      ]
    },
    {
      category: "2024",
      img: [
        {
          src: "/Icon/Rainbow/DisplaySample/img4.jpeg",
          alt: "img4" 
        },
        {
          src: "/Icon/Rainbow/DisplaySample/img5.jpeg",
          alt: "img5"
        },
        {
          src: "/Icon/Rainbow/DisplaySample/img6.jpeg",
          alt: "img6"
        }
      ]
    },
  ];

  const nextCategory = () => {
    setCategoryIndex((prevIndex) => (prevIndex + 1) % imgList.length);
  };

  const prevCategory = () => {
    setCategoryIndex((prevIndex) => (prevIndex - 1 + imgList.length) % imgList.length);
  };

  const handleClick = (src) => {
    console.log (`Image clicked: ${src}`);
   
    // You can replace the alert with any action you want to perform
  };

  return (
    <div className="min-h-screen text-white flex flex-col items-center py-4" style={{ background: 'linear-gradient(to right top, #051937, #004d7a, #008793, #00bf72, #a8eb12)' }}>
      <section id='Header' className="px-4">
        <div className="flex flex-col items-center mt-8">
          <img
            src="/Icon/Rainbow/icon.jpeg"
            alt="Profile Image"
            className="w-24 h-24 rounded-full"
          />
          <h1 className="text-2xl font-bold mt-4">Student Name</h1>
          <p className="text-lg font-bold text-white">Campus</p>
        </div>
      </section>
      <section id='Toolbar' className="px-4 text-center">
        <button onClick={() => window.location.href = '/rainbow/showcase'} className='bg-white text-black px-4 py-2 rounded-md m-6 '>
          Showcase
        </button>
        <div className="flex items-center justify-center mb-4">
          <button onClick={prevCategory} className="text-white mx-2">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="size-6">
              <path fillRule="evenodd" d="M7.72 12.53a.75.75 0 0 1 0-1.06l7.5-7.5a.75.75 0 1 1 1.06 1.06L9.31 12l6.97 6.97a.75.75 0 1 1-1.06 1.06l-7.5-7.5Z" clipRule="evenodd" />
            </svg>
          </button>
          <p className="font-bold text-white text-2xl">
            {imgList[categoryIndex].category}
          </p>
          <button onClick={nextCategory} className="text-white mx-2">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="size-6">
              <path fillRule="evenodd" d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </section>
      <section id='Gallery' className="mt-2 w-full max-w-md text-center">
        <div
          onMouseEnter={() => setVelocity(0)}
          onMouseLeave={() => setVelocity(20)}
        >
          <Marquee key={'Marquee'} velocity={velocity} resetAfterTries={0} pauseOnHover={true}>
            {imgList[categoryIndex].img.map((src, id) => (
              <div
                key={`marquee-example-${id}`}
                className="bg-gray-800 rounded-lg overflow-hidden mb-4 mx-2"
                style={{ width: 300 }}
                onClick={() => handleClick(src.src)}
              >
                <img
                  src={src.src}
                  alt={src.alt}
                  className="w-full cursor-pointer"
                />
              </div>
            ))}
          </Marquee>
        </div>
      </section>
    </div>
  );
}
