import React from 'react';
import { Head } from '@inertiajs/react';
import { useLocation } from 'react-router-dom';
import Header from '@/Pages/Components/Header/Header';
import Footer from '@/Pages/Components/Footer/Footer';
import ButtonGroup from '@/Pages/Components/FixedButtonGroup/ButtonGroup';
import LoginPage from '@/Pages/Components/UserPage/LoginPage';
import SigninPage from '@/Pages/Components/UserPage/SigninPage';
import '../../css/index.css';

const User = () => {
    const location = useLocation();
    const isLoginPage = location.pathname === '/user/login';
    const isSigninPage = location.pathname === '/user/register';
    return (  
        <>
            <Head title={import.meta.env.VITE_APP_NAME + " - " + "用戶登录"} />
            <div className='layout-container'>
                <ButtonGroup />
                <Header />
                {isLoginPage && <LoginPage />}
                {isSigninPage && <SigninPage />}
                <Footer />
            </div>
        </>
    );
};

export default User;