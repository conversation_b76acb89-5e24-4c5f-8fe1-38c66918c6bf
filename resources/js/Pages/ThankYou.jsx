import React, { useRef } from 'react';
import { Head, Link } from '@inertiajs/react';
import Header from '@/Pages/Components/Header/Header';
import Footer from '@/Pages/Components/Footer/Footer';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

const Thankyou = ({ invoice, auth }) => {
    const invoiceRef = useRef(null);

    const generatePDF = async () => {
        try {
            const element = invoiceRef.current;
            const canvas = await html2canvas(element, {
                scale: 2,
                logging: false,
                useCORS: true,
                allowTaint: true
            });
            
            const imgData = canvas.toDataURL('image/png');
            
            // A4 尺寸: 210 x 297 mm
            const pdf = new jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4'
            });

            const imgWidth = 210;
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            
            pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
            
            // 生成文件名
            const fileName = `invoice-${invoice.uuid}.pdf`;
            pdf.save(fileName);
            
        } catch (error) {
            console.error('生成 PDF 失敗:', error);
        }
    };
    

    return (
        <>
            <Head title="付款成功" />
            <div className="layout-container">
                <Header auth={auth} />
                <div className='border-t border-gray-200 py-10'>
                    <div className="container mx-auto py-12 px-4">
                        {/* 发票内容 */}
                        <div ref={invoiceRef} className="max-w-3xl mx-auto bg-white rounded-lg shadow-2xl p-8">
                            {/* Logo */}
                            <div className="flex justify-center mb-8">
                                <img
                                    src="/Icon/Logo.png"
                                    alt="Logo"
                                    width={150}
                                    height="auto"
                                    crossOrigin="anonymous"  // 添加这个属性以处理跨域图片
                                />
                            </div>

                            {/* 发票标题 */}
                            <div className="text-center mb-8">
                                <h1 className="text-3xl font-bold text-gray-900 mb-2">電子發票</h1>
                                <p className="text-gray-600">Digital Speed Print Workshop</p>
                            </div>

                            {/* 发票信息 */}
                            <div className="border-t border-b border-gray-200 py-6 mb-6">
                                <div className="flex justify-between mb-4">
                                    <span className="text-gray-600 font-medium">發票編號：</span>
                                    <span className="font-medium">{invoice.uuid}</span>
                                </div>
                                <div className="flex justify-between mb-4">
                                    <span className="text-gray-600 font-medium">付款時間：</span>
                                    <span className="font-medium">
                                        {new Date(invoice.paid_at).toLocaleString()}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 font-medium">付款金額：</span>
                                    <span className="font-bold text-xl text-second-color">
                                        ${invoice.total.toFixed(2)}
                                    </span>
                                </div>
                            </div>

                            {/* 商品列表 */}
                            <div className="mb-8">
                                <h2 className="text-lg font-semibold mb-4">帳單明細</h2>
                                <div className="space-y-4">
                                    {invoice.cart.map((item, index) => (
                                        <div key={index} className="flex items-center gap-4 border-b pb-4">
                                            <img 
                                                src={item.image.url} 
                                                alt={item.title}
                                                className="w-20 h-20 object-cover rounded-md"
                                                crossOrigin="anonymous"
                                            />
                                            <div className="flex-1">
                                                <h3 className="font-medium">{item.title}</h3>
                                                <p className="text-sm text-gray-600">
                                                    數量: {item.quantity}
                                                </p>
                                                <p className="text-sm text-gray-600">
                                                    單價: ${item.price}
                                                </p>
                                            </div>
                                            <div className="text-base text-gray-900 font-medium">
                                                小計：${item.price * item.quantity}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                {/* shipping */}
                                {invoice.order.shipping && (
                                    <div className="flex items-center justify-end py-4 gap-4">
                                        <p className="text-base text-gray-900 font-medium">運費：${invoice.order.shipping}</p>
                                    </div>
                                )}
                                {/* total amount */}
                                <div className="flex items-center border-t pt-4 justify-end gap-4">
                                    <p className="text-base text-gray-900 font-medium">總金額：${invoice.total.toFixed(2)}</p>
                                </div>
                            </div>

                            {/* Q&A Information */}
                            <div className="text-sm text-gray-500 mt-8 flex flex-col gap-2">
                                <p className="text-base font-medium text-gray-600 border-b pb-2 border-gray-600">常見問題</p>
                                <p className='font-medium text-gray-600'>1. 需要多長時間才能收到我的訂單？ 能夠加快發貨速度嗎？
                                訂單通常在 2 個工作日內發貨，並在發貨後 3-5 個工作日內送達。 我們將盡力盡快發出您的訂單，但無法加快包裹運輸速度。</p>
                                <p className='font-medium text-gray-600'>2. 如果包裹狀態顯示「已送達」，但我沒有收到，我該怎麼辦？
                                請聯絡快遞公司報告問題並在 7 天內立案。 然後，請在包裹顯示「已送達」後，盡快將您的案件編號和/或快遞公司的任何回覆轉發至<a href="mailto:<EMAIL>" className="inline-flex items-center gap-x-1.5 text-blue-600 decoration-2 hover:underline font-medium"><EMAIL></a>，我們將盡力提供幫助。</p>
                            </div>
                            {/* Footer Information */}
                            <div className="text-center text-sm text-gray-600 mt-8 font-medium">

                                <p>感謝您的惠顧</p>
                                <p>如有任何問題，請聯繫我們的客戶服務</p>
                                <p><a href="mailto:<EMAIL>" className="inline-flex items-center gap-x-1.5 text-blue-600 decoration-2 hover:underline font-medium"><EMAIL></a></p>
                            </div>
                        </div>

                        {/* 按钮组 */}
                        <div className="max-w-3xl mx-auto mt-8 flex justify-center gap-4">
                            <Link 
                                href="/user-order-info/waiting-for-delivery" 
                                className="px-6 py-3 bg-second-color text-white rounded-lg hover:bg-opacity-90"
                            >
                                查看訂單
                            </Link>
                            <button 
                                onClick={generatePDF}
                                className="px-6 py-3 bg-second-color text-white rounded-lg hover:bg-opacity-90"
                            >
                                下載發票
                            </button>
                            <Link 
                                href="/product" 
                                className="px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50"
                            >
                                繼續購物
                            </Link>
                        </div>
                    </div>
                </div>
                <Footer />
            </div>
        </>
    );
};

export default Thankyou;
