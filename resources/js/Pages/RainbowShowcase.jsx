import React, { useState, useEffect } from 'react';
import Marquee from "react-marquee-slider";

export default function RainboxShowcase() {
  const [velocity, setVelocity] = useState(10);
  const [categoryIndex, setCategoryIndex] = useState(0);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);

  useEffect(() => {
    if (dialogVisible) {
      document.body.classList.add('overflow-hidden');
    } else {
      document.body.classList.remove('overflow-hidden');
    }

    // Cleanup function to remove the class when the component unmounts
    return () => {
      document.body.classList.remove('overflow-hidden');
    };
  }, [dialogVisible]);

  const imgList = [
    {
      category: "2025",
      img: [
        {
          src: "/Icon/Rainbow/DisplaySample/img1.jpeg",
          alt: "img1"
        },
        {
          src: "/Icon/Rainbow/DisplaySample/img2.jpeg",
          alt: "img2"
        },
        {
          src: "/Icon/Rainbow/DisplaySample/img3.jpeg",
          alt: "img3"
        }
      ]
    },
    {
      category: "2024",
      img: [
        {
          src: "/Icon/Rainbow/DisplaySample/img4.jpeg",
          alt: "img4"
        },
        {
          src: "/Icon/Rainbow/DisplaySample/img5.jpeg",
          alt: "img5"
        },
        {
          src: "/Icon/Rainbow/DisplaySample/img6.jpeg",
          alt: "img6"
        }
      ]
    },
  ];

  const nextCategory = () => {
    setCategoryIndex((prevIndex) => (prevIndex + 1) % imgList.length);
  };

  const prevCategory = () => {
    setCategoryIndex((prevIndex) => (prevIndex - 1 + imgList.length) % imgList.length);
  };

  const handleClick = (src, alt) => {
    setSelectedImage(src);
    setDialogVisible(true);
  };

  const closeDialog = () => {
    setDialogVisible(false);
    setSelectedImage(null);
  };

  return (
    <div className="min-h-screen text-white flex flex-col items-center py-4" style={{ background: 'linear-gradient(to right top, #051937, #004d7a, #008793, #00bf72, #a8eb12)' }}>
      <section id='Navbar' className='text-center w-full'>
        <button onClick={() => window.location.href = '/rainbow/profile'} className='bg-white text-black px-4 py-2 rounded-md '>
          Back to Profile
        </button>
      </section>
      <section id='Header' className="px-4">
        <div className="flex flex-col items-center">
          <h1 className='text-2xl font-bold my-4'>Showcase</h1>
          <img
            src="/Icon/Rainbow/icon.jpeg"
            alt="Profile Image"
            className="w-24 h-24 rounded-full"
          />
          <h1 className="text-xl font-bold mt-4">Student Name</h1>
          <p className="text-lg font-bold text-white">Campus</p>
        </div>
      </section>
      <section id='Showcase' className='py-4'>
        <div className='flex flex-col items-center px-4'>
          <div className='flex flex-col gap-4'>
            {imgList.map((item, index) => (
              <div key={index}>
                <h2 className='text-base font-bold'>{item.category}</h2>
                <div className='flex flex-col gap-4 md:flex-row md:w-[32.2%] w-full'>
                  {item.img.map((img, index) => (
                    <img
                      className='w-full cursor-pointer'
                      key={index + img.src}
                      src={img.src}
                      alt={img.alt}
                      onClick={() => handleClick(img.src)}
                    />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {dialogVisible && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center px-4">
          <div className="bg-white p-4 rounded-md">
            <img src={selectedImage} alt="Selected" className="w-full" />
            <div className='flex flex-row justify-between'>
              <button onClick={() => window.location.href = `/rainbow/red-pocket?image=${selectedImage}`} className='bg-black text-white px-4 py-2 rounded-md mt-4'>
                利是封
              </button>
              <button onClick={closeDialog} className="mt-4 bg-black text-white px-4 py-2 rounded-md">
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
