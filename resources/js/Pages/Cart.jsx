import React, { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import message from 'antd/lib/message';
import Header from '@/Pages/Components/Header/Header';
import ButtonGroup from '@/Pages/Components/FixedButtonGroup/ButtonGroup';
import CartList from '@/Pages/Components/ShoppingCart/CartList';
import DeliveryInfoForm from '@/Pages/Components/Product/DeliveryInfoForm';
import DeliveryInfoDisplay from '@/Pages/Components/Product/DeliveryInfoDisplay';
import Footer from '@/Pages/Components/Footer/Footer';
import axios from 'axios';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);
const SHIPPING_FEE = 20;

const Cart = ({ auth, order }) => {
    const [messageApi, contextHolder] = message.useMessage();
    const [cartItems, setCartItems] = useState([]);
    const [deliveryInfo, setDeliveryInfo] = useState(auth.user?.delivery_info || null);
    const [deliveryMethod, setDeliveryMethod] = useState('delivery');
    const [clientSecret, setClientSecret] = useState('');
    const [isProcessing, setIsProcessing] = useState(false);
    const [userAddresses, setUserAddresses] = useState([]);
    const [totalPrice, setTotalPrice] = useState(0);

    // 获取购物车数据
    const fetchCartItems = async () => {
        try {
            const response = await axios.get('/cart/list');
            if (response.data.success) {
                const cart = response.data.data;
                if (cart?.cart_meta) {
                    setCartItems(cart.cart_meta);
                    calculateTotal(cart.cart_meta);
                }
            }
        } catch (error) {
            console.error('获取购物车数据失败:', error);
            messageApi.error('獲取購物車數據失敗');
        }
    };

    // 计算总价
    const calculateTotal = (items) => {
        const total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        setTotalPrice(total);
    };

    // 计算最终总价（包含运费）
    const finalTotalPrice = deliveryMethod === 'delivery' ? totalPrice + SHIPPING_FEE : totalPrice;

    useEffect(() => {
        if (order) {
            // 设置配送方式
            setDeliveryMethod(order.shipping > 0 ? 'delivery' : 'pickup');
            // 设置配送信息
            if (order.address) {
                setDeliveryInfo({
                    recipient: order.recipient || '',
                    phone: order.phone || '',
                    address: order.address || '',
                    note: order.note || ''
                });
            }
        } else {
            fetchCartItems();
        }
    }, [order]);

    // 获取用户地址
    useEffect(() => {
        const fetchUserAddresses = async () => {
            try {
                const response = await axios.get('/user/get-addresses');
                if (response.data.addresses) {
                    const addresses = Array.isArray(response.data.addresses) 
                        ? response.data.addresses 
                        : [response.data.addresses];
                    setUserAddresses(addresses);
                    
                    if (!order) {
                        const defaultAddress = addresses.find(addr => addr.is_default);
                        if (defaultAddress) {
                            setDeliveryInfo(defaultAddress);
                        }
                    }
                }
            } catch (error) {
                console.error('获取地址列表失败:', error);
            }
        };

        if (auth.user) {
            fetchUserAddresses();
        }
    }, [auth.user, order]);

    const handleCheckout = async () => {
        if (cartItems.length === 0) {
            messageApi.error('購物車是空的');
            return;
        }

        if (deliveryMethod === 'delivery' && !deliveryInfo) {
            messageApi.error('請填寫配送資訊');
            return;
        }

        setIsProcessing(true);
            // 准备订单数据
            const orderData = {
                order_no: order?.order_no || '',
                delivery_method: deliveryMethod,
                total_amount: finalTotalPrice,
                delivery_info: deliveryMethod === 'delivery' ? {
                    recipient: deliveryInfo.recipient,
                    phone: deliveryInfo.phone,
                    address: deliveryInfo.address,
                    note: deliveryInfo.note || ''
                } : null,
                items: cartItems.map(item => ({
                    product_id: item.product_id,
                    uuid: item.local_uuid,
                    title: item.product_name,
                    price: item.price,
                    quantity: item.quantity,
                    specs: item.specs || {},
                    image: item.product_image,
                }))
            };
            console.log(cartItems,'cartItems');
            // 创建支付意图
        try {
            const response = await axios.post('/checkout', {
                // items: cartItems,
                // delivery_method: deliveryMethod,
                // delivery_info: deliveryInfo,
                // shipping_fee: deliveryMethod === 'delivery' ? SHIPPING_FEE : 0
                amount: finalTotalPrice,
                orderData: orderData,
                currency: 'hkd'
            });

            if (response.data.clientSecret) {
                setClientSecret(response.data.clientSecret);
            }
        } catch (error) {
            console.error('Checkout error:', error);
            messageApi.error('結帳失敗');
        } finally {
            setIsProcessing(false);
        }
    };

    const deliveryOptions = [
        {
            value: 'delivery',
            label: '宅配到府',
            description: '將商品配送到指定地址(需要大約4-10個工作天)'
        },
        // {
        //     value: 'pickup',
        //     label: '門市取貨',
        //     description: '可至指定門市取貨'
        // }
    ];

    const handlePaymentSuccess = async (paymentIntent) => {
        try {
            const response = await axios.post('/confirm-payment', {
                payment_intent_id: paymentIntent.id,
                transaction_id: paymentIntent.id,
                order_no: order?.order_no || ''
            });

            if (response.data.success) {
                messageApi.success('訂單已成功建立');
                setCartItems([]);
                // 重定向到感谢页面
                window.location.href = response.data.redirect_url;
            }
        } catch (error) {
            console.error('確認訂單失敗:', error);
            messageApi.error('確認訂單失敗，請聯繫客服');
        }
    };

    return (
        <>
            <Head title="購物車" />
            <div className='layout-container'>
                {contextHolder}
                <ButtonGroup />
                <Header />
                <div className="container mx-auto p-4">
                    <div className="flex flex-col gap-8">
                        <div className="">
                            <CartList />
                        </div>
                        <div className="">
                            {order && (
                            <div className='md:px-8 px-4'>
                                    <div className=" bg-white rounded-lg shadow p-6">
                                        <h2 className="text-xl font-semibold mb-2">訂單資訊</h2>
                                        <div className="text-gray-600">
                                            <p>訂單編號: {order.order_no}</p>
                                            <p>下單時間: {new Date(order.order_date).toLocaleString()}</p>
                                        </div>
                                    </div>
                            </div>
                            )}
                            
                            {/* 配送方式 */}
                            <div className="mt-12 md:px-8 px-0">
                                <div className="p-4 bg-white rounded-lg shadow">
                                    <h3 className="text-lg font-semibold mb-4">配送方式</h3>
                                    <div className="space-y-3">
                                        {/* 配送方式选择 */}
                                        {deliveryOptions.map((option) => (
                                            <div
                                                key={option.value}
                                                className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                                                    deliveryMethod === option.value
                                                        ? 'border-second-color bg-second-color bg-opacity-10'
                                                        : 'border-gray-200 hover:border-gray-300'
                                                }`}
                                                onClick={() => setDeliveryMethod(option.value)}
                                            >
                                                <div className="flex items-center gap-3">
                                                    <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                                                        deliveryMethod === option.value
                                                            ? 'border-second-color'
                                                            : 'border-gray-400'
                                                    }`}>
                                                        {deliveryMethod === option.value && (
                                                            <div className="w-2 h-2 rounded-full bg-second-color" />
                                                        )}
                                                    </div>
                                                    <div>
                                                        <div className="font-medium">{option.label}</div>
                                                        <div className="text-sm text-gray-500">{option.description}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                                {/* 费用明细 */}
                                <div className="mt-8 bg-white rounded-lg shadow p-4">
                                    <h3 className="md:text-lg text-base font-semibold mb-4">費用明細</h3>
                                    <div className="space-y-2">
                                        <div className="flex justify-between md:text-base text-sm">
                                            <span>商品總額</span>
                                            <span>${totalPrice.toFixed(2)}</span>
                                        </div>
                                        {deliveryMethod === 'delivery' && (
                                            <div className="flex justify-between md:text-base text-sm">
                                                <span>運費</span>
                                                <span>${SHIPPING_FEE.toFixed(2)}</span>
                                            </div>
                                        )}
                                        <div className="border-t pt-2 mt-2">
                                            <div className="flex justify-between font-bold md:text-lg text-base">
                                                <span>應付總額</span>
                                                <span className="text-second-color">${finalTotalPrice.toFixed(2)}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {deliveryMethod === 'delivery' && (
                                <>
                                    <div className="mt-4">
                                        <div className="p-4 bg-white rounded-lg shadow">
                                            <h3 className="md:text-lg text-base font-semibold mb-4">配送資訊</h3>
                                            <DeliveryInfoForm 
                                                onSuccess={setDeliveryInfo}
                                                initialValues={deliveryInfo}
                                                userAddresses={userAddresses}
                                            />
                                        </div>
                                    </div>

                                    {deliveryInfo && (
                                        <div className="mt-4">
                                            <DeliveryInfoDisplay 
                                                deliveryInfo={deliveryInfo}
                                                showEditButton={false}
                                            />
                                        </div>
                                    )}
                                </>
                            )}

                            {/* 支付表单 */}
                            {clientSecret && (
                                <div className="mt-8 bg-white rounded-lg shadow p-6">
                                    <h3 className="text-lg font-semibold mb-4">付款資訊</h3>
                                    <Elements stripe={stripePromise} options={{ clientSecret }}>
                                        <PaymentForm 
                                            onSuccess={handlePaymentSuccess}
                                            isProcessing={isProcessing}
                                        />
                                    </Elements>
                                </div>
                            )}

                            {/* 结账按钮 */}
                                <div className="mt-8 bg-white rounded-lg shadow p-6">
                                    <div className="flex justify-between items-center">
                                        <div className="text-xl">
                                            <span className='text-lg font-bold'>總計:</span> <span className="font-bold text-second-color text-xl">${finalTotalPrice.toFixed(2)}</span>
                                        </div>
                                        {!clientSecret && (
                                            <button
                                            onClick={handleCheckout}
                                            disabled={cartItems.length === 0 || isProcessing}
                                            className={`px-8 py-3 rounded-lg text-white font-medium ${
                                                cartItems.length === 0 || isProcessing
                                                    ? 'bg-gray-400 cursor-not-allowed'
                                                    : 'bg-second-color hover:bg-opacity-90'
                                            }`}
                                        >
                                                {isProcessing ? '處理中...' : '結帳'}
                                            </button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <Footer />
            </div>
        </>
    );
};

// 支付表单组件
const PaymentForm = ({ onSuccess, isProcessing }) => {
    const stripe = useStripe();
    const elements = useElements();
    const [error, setError] = useState(null);

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!stripe || !elements) return;

        const { error, paymentIntent } = await stripe.confirmPayment({
            elements,
            // 跳轉到感謝頁面
            redirect: 'if_required'
        });

        if (error) {
            setError(error.message);
        } else if (paymentIntent.status === 'succeeded') {
            onSuccess(paymentIntent);
        }
    };

    return (
        <form onSubmit={handleSubmit}>
            <PaymentElement />
            {error && <div className="text-red-500 mt-2">{error}</div>}
            <button
                type="submit"
                disabled={!stripe || isProcessing}
                className="mt-4 w-full bg-second-color text-white py-2 rounded-lg hover:bg-opacity-90 disabled:bg-gray-400"
            >
                {isProcessing ? '處理中...' : '確認付款'}
            </button>
        </form>
    );
};

export default Cart;
