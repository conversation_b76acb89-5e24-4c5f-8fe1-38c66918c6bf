"use client";

import { <PERSON> } from "react-router-dom";
import '../../../../css/index.css';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';

const HomeAbout = () => {
    return (
        <div className="px-4 bg-gradient-to-b from-white to-[#F2F3F3] container-py md:pb-0 pb-12">
            <div className="relative md:py-14 py-0">
                <div className='top-container flex md:flex-row flex-col-reverse md:gap-0 gap-4'>
                    <div className='left md:w-[60%] w-[100%] text-center inline-flex flex-col md:top-[-50px] relative'>
                        <span className="about-title md:text-6xl text-4xl mb-4 font-bold second-color">Design.</span>
                        <span className="about-title md:text-6xl text-4xl mb-4 font-bold main-color">Create.</span>
                        <span className="about-title md:text-6xl text-4xl md:mb-0 mb-4 font-bold text-[#D9D9D9]">Inspire.</span>
                    </div>
                    <div className='right w-full relative md:min-h-[230px] min-h-[0px] md:py-2 py-4 md:w-[40%]'>
                        <img
                            src="/HomePage/Banner.png"
                            alt="Banner"
                        />
                    </div>
                </div>
                <div className='bottom-container flex md:flex-row flex-col md:min-h-[300px]'>
                    <div className='left md:w-[60%] w-[100%] text-center inline-flex flex-col relative md:min-h-[300px]'>
                        <img
                            src="/HomePage/Banner.png"
                            alt="Banner"
                        />
                    </div>
                    <div className='right md:w-[40%] w-full relative flex justify-center flex-col'>
                        <p className='md:text-xl text-sm md:p-8 px-0 py-4 about-text'>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Faucibus in libero risus semper habitant arcu eget. Et integer facilisi eget.</p>
                        <div className='md:ml-5 ml-0 button-container inline-flex items-baseline'>
                            <Link className='p-4 button text-white rounded-full bg-second-color text-base sm:text-2xl cursor-pointer' href="https://www.google.com" target="_blank">
                                了解更多
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default HomeAbout;