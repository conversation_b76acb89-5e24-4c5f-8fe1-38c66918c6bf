// import Link from 'next/link';
// import Image from 'next/image';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import { Link } from "react-router-dom";
import '../../../../css/index.css';
import '../../../../css/home.css';
import './HomeGSAPScroll';
const HomeProduct = () => {
    const productList = [
        {
            id: 1,
            title: '新品上市',
            description: 'DIY Delight for Ladies DIY Delight for Ladies',
            content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Faucibus in libero risus semper habitant arcu eget. Et integer facilisi eget.',
            image: '/HomePage/Product1.jpg',
        },
        {
            id: 2,
            title: '人氣商品',
            description: 'DIY Delight for Ladies DIY Delight for Ladies',
            content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Faucibus in libero risus semper habitant arcu eget. Et integer facilisi eget.',
            image: '/HomePage/Product2.png',
        },
        {
            id: 3,
            title: '推薦商品',
            description: 'DIY Delight for Ladies DIY Delight for Ladies',
            content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Faucibus in libero risus semper habitant arcu eget. Et integer facilisi eget.',
            image: '/HomePage/Product3.jpg',
        },
    ]
    return (
        <div className='md:px-5 px-0'>
            <Swiper className=' w-full home-product-swiper' id='home-product-swiper'
                autoplay={{delay: 2000 }}
                spaceBetween={20}
                slidesPerView={3}
                centeredSlides={true}
                onSlideChange={(swiper) =>{
                    if(swiper.activeIndex === 0){
                        swiper.slideTo(1);
                    }
                }}
                onSwiper={(swiper) =>{
                    if(swiper){
                        swiper.slideTo(1);
                    }
                }}
                breakpoints={{
                    1024: {
                        slidesPerView: 3,
                    },
                    0:{
                        slidesPerView: 1,
                    }
                }}
            >
                {/* Fake Slide */}
                <SwiperSlide className='product-slide'>
                    <div className='p-0 h-full flex items-center gap-4 product-slide-box'>
                        <div className='left w-[55%] p-2 rounded-2xl bg-main-color'>
                            <a href="/product" className='w-full rounded-2xl'>
                                <img
                                    src="/HomePage/Product1.jpg"
                                    alt="Banner"
                                    className='w-full rounded-2xl'
                                    layout="responsive"
                                    width={100}
                                    height={150}
                                />
                            </a>
                        </div>
                        <div className='right w-[45%] flex gap-4 flex-col'>
                            <div className='pl-5 relative title text-white text-4xl sm:text-4xl line-clamp-2 font-bold after:absolute after:w-2 after:h-2 after:rounded-full after:top-1/2 after:left-0 after:translate-y-[-50%] after:bg-[#DEDC00]'>Totebag</div>
                            <div className='pl-5 description text-white text-lg sm:text-2xl line-clamp-2'>DIY Delight for Ladies DIY Delight for Ladies</div>
                            <div className='pl-5 content text-white text-sm sm:text-lg font-light line-clamp-5'>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Faucibus in libero risus semper habitant arcu eget. Et integer facilisi eget.</div>
                            <div className='ml-5 button-container inline-flex items-baseline'>
                                <Link className='md:p-4 p-2 button text-white rounded-full bg-second-color font-bold text-base sm:text-xl cursor-pointer' href="/product" target="_blank">
                                    了解更多
                                </Link>
                            </div>
                        </div>
                    </div>
                </SwiperSlide>
                {/* Real Slide */}
                {productList.map((item, index) => (
                    <SwiperSlide className='product-slide' key={index}>
                        <div className='p-0 h-full flex items-center gap-4 product-slide-box'>
                            <div className='left w-[55%] p-2 rounded-2xl bg-main-color transition-all duration-200'>
                                <a href={`/product?tab=${item.title}`} className='w-full rounded-2xl'>
                                    <img
                                        src={item.image}
                                        alt="Banner"
                                        className='w-full rounded-2xl'
                                        layout="responsive"
                                        width={100}
                                        height={150}
                                    />
                                </a>
                            </div>
                            <div className='px-0 md:px-4 right w-[45%] flex gap-4 flex-col relative transition-all duration-300'>
                                <div className='pl-2 md:pl-5 relative title text-white text-2xl sm:text-4xl line-clamp-2 font-bold text-animate'>{item.title}</div>
                                {/* after:left-0 after:translate-y-[-50%] after:bg-[#DEDC00] */}
                                {/* after:absolute after:w-2 after:h-2 after:rounded-full after:top-1/2  */}
                                <div className='pl-2 md:pl-5 description text-white text-lg sm:text-2xl line-clamp-2 text-animate'>{item.description}</div>
                                <div className='pl-2 md:pl-5 content text-white text-sm sm:text-lg font-light line-clamp-5 text-animate'>{item.content}</div>
                                <div className='ml-2 md:ml-5 button-container inline-flex items-baseline text-animate'>
                                    <a href={`/product?tab=${item.title}`} className='md:p-4 p-4 button text-white rounded-full bg-second-color font-bold text-base sm:text-xl cursor-pointer'>
                                        了解更多
                                    </a>
                                </div>
                            </div>
                        </div>
                    </SwiperSlide>
                ))}
            </Swiper>
        </div>
    );
};


export default HomeProduct;

