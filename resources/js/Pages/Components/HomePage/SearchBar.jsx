"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Link } from "react-router-dom";
import '../../../../css/index.css';

const SearchBar = ({ isSearchOpen, setIsSearchOpen }) => {
    const [searchStr, setSearchStr] = useState('');
    const [isMounted, setIsMounted] = useState(false);
    const searchRef = useRef(null);
    const isComponentMounted = useRef(true);
    
    useEffect(() => {
        setIsMounted(true);
        
        const handleClickOutside = (event) => {
            if (!isComponentMounted.current) return;
            
            if (searchRef.current && 
                !searchRef.current.contains(event.target) && 
                !event.target.closest('[data-search-trigger]') &&
                typeof setIsSearchOpen === 'function'
            ) {
                setIsSearchOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        
        return () => {
            isComponentMounted.current = false;
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [setIsSearchOpen]);

    const handleSearch = (e) => {
        e.preventDefault();
    };

    if (!isMounted) {
        return null;
    }

    return (
        <div 
            ref={searchRef}
            style={{ 
                maxHeight: isSearchOpen ? '200px' : '0',
                visibility: isSearchOpen ? 'visible' : 'hidden',
                padding: isSearchOpen ? '30px 0 30px 0' : '0'
            }}
            className={`search-bar main-color bg-[#FBFBFB] px-6 transition-all duration-300 overflow-hidden`}
        >
            <div className='border border-[#008891] rounded-full px-2 py-2'>
                <form className='flex items-center justify-between w-full' onSubmit={handleSearch}>
                    <div className='relative flex items-center space-x-4 main-color flex-1 px-2
                    before:absolute before:w-[1px] before:h-full before:bg-[#E7EAEA] before:left-[60px] before:top-1/2 before:-translate-y-1/2'>
                        <img
                            src="/Icon/Search.png"
                            alt="Search Logo"
                            width={40}
                            height={40}
                        />
                        <input
                            className='w-full bg-[#FBFBFB] h-10 p-2 focus:outline-none .placeholder-[E7EAEA]'
                            type="text"
                            placeholder="利是封"
                            value={searchStr}
                            onChange={(e) => setSearchStr(e.target.value)}
                        />
                    </div>
                    <div className='bg-main-color text-white px-10 py-4 rounded-full'>
                        <button type="submit">搜索</button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default SearchBar;
