"use client";

import { useEffect, useState } from 'react';
import { Link } from "react-router-dom";
import '../../../../css/index.css';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';

const HomeBanner = () => {
    // const [isMounted, setIsMounted] = useState(false);

    // useEffect(() => {
    //     setIsMounted(true);
    // }, []);

    // if (!isMounted) {
    //     return null; // 或者返回一个加载指示器
    // }

    return (
        <div className="">
            <Swiper
                className='max-h-[300px] w-full'
                autoplay={{
                    delay: 2500,
                    disableOnInteraction: false,
                }}
                spaceBetween={0}
                slidesPerView={1}
                onSlideChange={() => console.log('slide change')}
                onSwiper={(swiper) => console.log(swiper)}
            >
                <SwiperSlide>
                    <Link href="/">
                        <img
                            src="/HomePage/Banner.png"
                            alt="Banner"
                        />
                    </Link>
                </SwiperSlide>
                <SwiperSlide className='h-[300px] w-[300px]'>
                    <Link href="/">
                        <img
                            src="/HomePage/Banner.png"
                            alt="Banner"
                        />
                    </Link>
                </SwiperSlide>
            </Swiper>
        </div>
    );
};

export default HomeBanner;
