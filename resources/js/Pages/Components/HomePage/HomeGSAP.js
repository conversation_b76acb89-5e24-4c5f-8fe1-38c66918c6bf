import gsap from 'gsap';

export const initializeGSAP = () => {
    const handleAboutText = (textContainer) => {
        const rightSection = textContainer.querySelector('.right');
        if (!rightSection) {
            return;
        }
        const textElement = rightSection.querySelector('.about-text');
        const buttonContainer = rightSection.querySelector('.button-container');

        if (textElement) {
            let currentWordSpans = [];
            let isAnimating = false;

            const setupTextAnimation = () => {
                const originalText = textElement.textContent;
                textElement.innerHTML = '';
                
                const words = originalText.split(' ').filter(word => word.length > 0);
                currentWordSpans = [];
                
                words.forEach((word, wordIndex) => {
                    const maskContainer = document.createElement('div');
                    maskContainer.style.display = 'inline-block';
                    maskContainer.style.overflow = 'hidden';
                    maskContainer.style.verticalAlign = 'top';
                    maskContainer.style.padding = '0 2px';
                    
                    const wordSpan = document.createElement('span');
                    wordSpan.textContent = word + ' ';
                    wordSpan.style.display = 'inline-block';
                    
                    currentWordSpans.push(wordSpan);
                    maskContainer.appendChild(wordSpan);
                    textElement.appendChild(maskContainer);
                    
                    gsap.set(wordSpan, {
                        yPercent: 100,
                        opacity: 0
                    });
                });
            };

            const playAnimation = () => {
                if (isAnimating) return;
                isAnimating = true;

                currentWordSpans.forEach((wordSpan, wordIndex) => {
                    gsap.to(wordSpan, {
                        yPercent: 0,
                        opacity: 1,
                        duration: 0.8,
                        delay: wordIndex * 0.05,
                        ease: "power3.out"
                    });
                });
            };

            const resetAnimation = () => {
                isAnimating = false;
                currentWordSpans.forEach((wordSpan) => {
                    gsap.to(wordSpan, {
                        yPercent: 100,
                        opacity: 0,
                        duration: 0.3,
                        ease: "power2.in"
                    });
                });
            };

            // 初始设置
            setupTextAnimation();

            // 创建观察者
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // 进入视口时播放动画
                        playAnimation();
                    } else {
                        // 离开视口时重置动画
                        resetAnimation();
                    }
                });
            }, {
                threshold: 0.5,
                rootMargin: '0px'
            });

            observer.observe(textElement);
        }

        // 按钮动画
        if (buttonContainer) {
            let isButtonAnimating = false;

            const buttonObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !isButtonAnimating) {
                        isButtonAnimating = true;
                        
                        gsap.set(buttonContainer, {
                            y: 50,
                            opacity: 0
                        });

                        gsap.to(buttonContainer, {
                            y: 0,
                            opacity: 1,
                            duration: 0.8,
                            delay: 0.5,
                            ease: "power3.out"
                        });
                    } else if (!entry.isIntersecting) {
                        isButtonAnimating = false;
                        gsap.to(buttonContainer, {
                            y: 50,
                            opacity: 0,
                            duration: 0.3,
                            ease: "power2.in"
                        });
                    }
                });
            }, {
                threshold: 0.5,
                rootMargin: '0px'
            });

            buttonObserver.observe(buttonContainer);
        }
    };
    const handleGuideAnimation = () => {
        const guideContainer = document.querySelector('.guide-container');
        if (!guideContainer) return;

        const guideItems = guideContainer.children;
        console.log(guideItems,'guideItems');
        
        Array.from(guideItems).forEach((item, index) => {
            // 使用更可靠的选择器
            const numberElement = item.querySelector('.second-color');
            const textElement = item.children[1]; // 直接选择第二个子元素

            // 设置初始状态
            gsap.set(item, {
                opacity: 0,
                y: 50
            });

            if (numberElement) {
                gsap.set(numberElement, {
                    opacity: 0,
                    x: -30
                });
            }

            if (textElement) {
                gsap.set(textElement, {
                    opacity: 0,
                    x: 30
                });
            }

            // 创建观察者
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // 创建动画时间线
                        const tl = gsap.timeline({
                            defaults: {
                                ease: "power3.out"
                            }
                        });

                        // 添加动画序列
                        tl.to(item, {
                            opacity: 1,
                            y: 0,
                            duration: 0.6
                        });

                        if (numberElement) {
                            tl.to(numberElement, {
                                opacity: 1,
                                x: 0,
                                duration: 0.4
                            }, "-=0.3");
                        }

                        if (textElement) {
                            tl.to(textElement, {
                                opacity: 1,
                                x: 0,
                                duration: 0.4
                            }, "-=0.2");
                        }

                    } else {
                        // 离开视口时重置
                        gsap.to(item, {
                            opacity: 0,
                            y: 50,
                            duration: 0.3
                        });

                        if (numberElement) {
                            gsap.to(numberElement, {
                                opacity: 0,
                                x: -30,
                                duration: 0.3
                            });
                        }

                        if (textElement) {
                            gsap.to(textElement, {
                                opacity: 0,
                                x: 30,
                                duration: 0.3
                            });
                        }
                    }
                });
            }, {
                threshold: 0.3,
                rootMargin: '-10% 0px'
            });

            observer.observe(item);
        });
    };

    const handleRecommendAnimation = () => {
        const checkAndAnimateProducts = () => {
            const productItems = document.querySelectorAll('.product-list-container .grid .group');
            
            if (productItems.length === 0) {
                // 如果还没有产品项，等待100ms后重试
                setTimeout(checkAndAnimateProducts, 100);
                return;
            }

            productItems.forEach((item, index) => {
                // 获取元素
                const image = item.querySelector('img');
                const title = item.querySelector('.main-color');
                const size = item.querySelector('.text-gray-500');
                const detail = item.querySelector('.line-clamp-2');
                const price = item.querySelector('.second-color');

                // 设置初始状态
                gsap.set(item, {
                    opacity: 0,
                    scale: 0.8,
                    y: 50
                });

                gsap.set(image, {
                    scale: 1.2,
                    opacity: 0
                });

                gsap.set([title, size, price, detail], {
                    y: 20,
                    opacity: 0
                });

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // 创建动画时间线
                            const tl = gsap.timeline({
                                defaults: {
                                    ease: "power3.out"
                                }
                            });

                            // 添加动画序列
                            tl.to(item, {
                                opacity: 1,
                                scale: 1,
                                y: 0,
                                duration: 0.6,
                                delay: index * 0.1 // 依次出现
                            })
                            .to(image, {
                                scale: 1,
                                opacity: 1,
                                duration: 0.8
                            }, "-=0.3")
                            .to(title, {
                                y: 0,
                                opacity: 1,
                                duration: 0.4
                            }, "-=0.4")
                            .to(size, {
                                y: 0,
                                opacity: 1,
                                duration: 0.4
                            }, "-=0.3")
                            
                            .to(detail, {
                                y: 0,
                                opacity: 1,
                                duration: 0.4
                            }, "-=0.2")
                            .to(price, {
                                y: 0,
                                opacity: 1,
                                duration: 0.4
                            }, "-=0.1");

                            // 添加悬停效果
                            item.addEventListener('mouseenter', () => {
                                gsap.to(item, {
                                    scale: 1,
                                    duration: 0.3,
                                    ease: "power2.out"
                                });
                                gsap.to(image, {
                                    scale: 1.1,
                                    duration: 0.3
                                });
                            });

                            item.addEventListener('mouseleave', () => {
                                gsap.to(item, {
                                    scale: 1,
                                    duration: 0.3,
                                    ease: "power2.out"
                                });
                                gsap.to(image, {
                                    scale: 1,
                                    duration: 0.3
                                });
                            });

                        } else {
                            // 离开视口时重置
                            gsap.to(item, {
                                opacity: 0,
                                scale: 0.8,
                                y: 50,
                                duration: 0.3
                            });
                            gsap.set(image, {
                                scale: 1.2,
                                opacity: 0
                            });
                            gsap.set([title, size, price, detail], {
                                y: 20,
                                opacity: 0
                            });
                        }
                    });
                }, {
                    threshold: 0.2,
                    rootMargin: '-10% 0px'
                });

                observer.observe(item);
            });
        };

        // 开始检查
        checkAndAnimateProducts();
    };

    // 执行所有初始化
    const aboutSection = document.querySelector('.bottom-container');
    if (aboutSection) {
        handleAboutText(aboutSection);
    }
    handleGuideAnimation();
    handleRecommendAnimation();
};

export default initializeGSAP;
