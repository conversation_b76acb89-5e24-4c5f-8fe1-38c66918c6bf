import gsap from 'gsap';
import ScrollTrigger from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export const initializeGSAP = () => {
    // 清除所有现有的 ScrollTrigger
    ScrollTrigger.getAll().forEach(st => st.kill());
    // 创建滚动监听器
    const handleScroll = () => {
        const scrollY = window.scrollY;
        
        // 处理 topic-container 元素
        document.querySelectorAll('.topic-container').forEach((section) => {
            handleSection(section, scrollY);
        });

        // 处理 home-product-swiper 元素
        document.querySelectorAll('.home-product-swiper').forEach((swiper) => {
            // 获取元素位置信息
            handleSwiper(swiper, scrollY);
        });
        document.querySelectorAll('.about-title').forEach((aboutTitle) => {
            // 获取元素位置信息
            handleAboutTitle(aboutTitle, scrollY);
        });
        // document.querySelectorAll('.right').forEach((aboutTitle) => {
        //     // 获取元素位置信息
        //     handleAboutText(aboutTitle, scrollY);
        // });
    };

    // 处理单个 section 的函数
    const handleSection = (section, scrollY) => {
        const rect = section.getBoundingClientRect();
        const sectionTop = rect.top + window.pageYOffset;
        const sectionHeight = rect.height;
        const viewportHeight = window.innerHeight;

        const distanceFromTop = scrollY - sectionTop + viewportHeight;
        const scrollProgress = distanceFromTop / (sectionHeight + viewportHeight);

        if (distanceFromTop > 0 && distanceFromTop < (sectionHeight + viewportHeight)) {
            const xMove = Math.min(100, Math.max(-100, (scrollProgress - 0.5) * 100));
            const centerPoint = 0.5;
            const distanceFromCenter = Math.abs(scrollProgress - centerPoint);
            const opacity = Math.max(0.2, 1 - 0.8 * distanceFromCenter);

            // 标题和副标题动画
            const title = section.querySelector('.t1');
            const subtitle = section.querySelector('.t2');

            if (title) {
                gsap.to(title, {
                    x: xMove * 0.5,
                    opacity: opacity,
                    duration: 0.1,
                    ease: "none"
                });
            }

            if (subtitle) {
                gsap.to(subtitle, {
                    x: -xMove * 0.3,
                    opacity: opacity,
                    duration: 0.1,
                    ease: "none"
                });
            }
        }
    };
    const handleSwiper = (swiper, scrollY) => {
        const rect = swiper.getBoundingClientRect();
        const swiperTop = rect.top + window.pageYOffset;
        const swiperHeight = rect.height;
        const viewportHeight = window.innerHeight;

        // 计算元素进入视口的距离
        const distanceFromTop = scrollY - swiperTop + viewportHeight;
        const scrollProgress = distanceFromTop / (swiperHeight + viewportHeight);

        // 只有当元素接近视口时才应用动画
        if (distanceFromTop > 0 && distanceFromTop < (swiperHeight + viewportHeight)) {
            // 计算与视口中心的距离
            const centerPoint = 0.5;
            const distanceFromCenter = Math.abs(scrollProgress - centerPoint);
            
            // 计算透明度：在中心点时为1，离开中心点时逐渐减小
            const opacity = Math.max(0.2, 1 - distanceFromCenter);

            // 计算Y轴位移：在中心点时为0，离开中心点时向上或向下移动
            // md: 200, sm: 100

            const yMove = (scrollProgress - centerPoint) * (window.innerWidth < 768 ? 100 : 200); // 200是最大移动离

            // 应用动画
            gsap.to(swiper, {
                y: -yMove,
                opacity: opacity,
                duration: 0.1,
                ease: "none"
            });
        }
    }
    const handleAboutTitle = (title, scrollY) => {
        const rect = title.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportCenter = viewportHeight / 2 - 200;

        // 计算元素顶部到视口中心的距离
        const distanceToCenter = rect.top - viewportCenter;
        
        // Phase 1: 元素接近视口中心之前
        if (distanceToCenter > 0) {
            // 计算接近中心的进度 (0 到 1)
            const approachProgress = Math.max(0, Math.min(1, 1 - (distanceToCenter / viewportHeight)));
            
            gsap.to(title, {
                x: -50 * (1 - approachProgress), // 从下方向上移动
                // opacity: -0.2 + (approachProgress * 0.8), // 逐渐变得更不透明
                duration: 0.1,
                ease: "none"
            });
        }
        // Phase 2: 元素到达或超过视口中心
        else {
            // 计算第二阶段的效果
            const phase2Progress = Math.abs(distanceToCenter) / viewportCenter;
            const yMove = Math.max(0, phase2Progress * 100); // 向上移动
            const opacity = Math.max(0.2, 1 - (phase2Progress * 0.8)); // 逐渐变透明

            gsap.to(title, {
                y: -yMove,
                opacity: opacity,
                duration: 0.1,
                ease: "easeInOut"
            });
        }
    };
    const handleAboutText = (textContainer, scrollY) => {
        const textElements = textContainer.querySelectorAll('.right .about-text');
        
        textElements.forEach((textElement, index) => {
            const originalText = textElement.textContent;
            textElement.innerHTML = '';
            
            const words = originalText.split(' ').filter(word => word.length > 0);
            const wordSpans = [];
            
            words.forEach((word, wordIndex) => {
                const maskContainer = document.createElement('div');
                maskContainer.style.display = 'inline-block';
                maskContainer.style.overflow = 'hidden';
                maskContainer.style.verticalAlign = 'top';
                maskContainer.style.padding = '0 2px';
                
                const wordSpan = document.createElement('span');
                wordSpan.textContent = word + ' ';
                wordSpan.style.display = 'inline-block';
                
                wordSpans.push(wordSpan);
                maskContainer.appendChild(wordSpan);
                textElement.appendChild(maskContainer);
                
                // 设置初始状态
                gsap.set(wordSpan, {
                    yPercent: 100,
                    opacity: 0
                });

                // 执行一次性动画
                gsap.to(wordSpan, {
                    yPercent: 0,
                    opacity: 1,
                    duration: 0.8,
                    delay: wordIndex * 0.05,
                    ease: "power3.out"
                });
            });
        });
    };

    // 添加滚动事件监听器
    window.addEventListener('scroll', handleScroll);
    // 初始运行一次以设置初始状态
    handleScroll();

    // 添加 resize 事件监听器
    window.addEventListener('resize', handleScroll);

    // 返回清理函数
    return () => {
        window.removeEventListener('scroll', handleScroll);
        window.removeEventListener('resize', handleScroll);
        ScrollTrigger.getAll().forEach(st => st.kill());
    };
};
