"use client";

import { useEffect, useRef } from 'react';
import '../../../../css/index.css';
import '../../../../css/home.css';

const HomeGuide = () => {
    const videoRef = useRef(null);
    const playButtonRef = useRef(null);
    const content = [
        {
            content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Faucibus in libero risus...',
        },
        {
            content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Faucibus in libero risus...',
        },
        {
            content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Faucibus in libero risus...',
        },
    ]

    useEffect(() => {
        const handleClick = () => {
            if (videoRef.current) {
                videoRef.current.play();
                playButtonRef.current.style.display = 'none';
            }
        };

        const playButton = playButtonRef.current;
        if (playButton) {
            playButton.addEventListener('click', handleClick);
        }

        return () => {
            if (playButton) {
                playButton.removeEventListener('click', handleClick);
            }
        };
    }, []);

    return (
        <div className="container-py rounded-3xl md:mt-[-15px] mt-[-15px] bg-white relative ">
            <div className="relative w-full">
                <div className='topic-container text-center md:py-20 py-4'>
                    <div className='t1 text-3xl md:text-5xl font-bold main-color'>使用方法</div>
                    <div className='t2 text-lg md:text-xl mt-2 md:mt-4 main-color text-transform: uppercase'>How To Use</div>
                </div>
                <div className='middle-container relative w-full'>
                    <img
                        ref={playButtonRef}
                        className="absolute top-1/2 left-1/2 z-10 translate-y-[-50%] translate-x-[-50%] cursor-pointer"
                        src="/HomePage/playbutton.png"
                        alt="视频播放按钮"
                        width={50}
                        height={50}
                    />
                    <div className="video-wrapper">
                        <video 
                            ref={videoRef}
                            muted 
                            loop 
                            playsInline 
                            preload="metadata" 
                            className="video-element"
                            poster="/HomePage/video_thumbnail.png"
                        >
                            <source src="/HomePage/sample.mp4" type="video/mp4" />
                            <track
                                src="/path/to/captions.vtt"
                                kind="subtitles"
                                srcLang="en"
                                label="English"
                            />
                            您的浏览器不支持视频标签。
                        </video>
                    </div>
                </div>
                <div className='bottom-container guide-container md:my-10 my-5'>
                    {content.map((item, index) => (
                        <div key={index} className='mb-5 pb-5 relative after:absolute after:bottom-0 after:left-0 after:w-full after:h-[1px] after:bg-[#D8D8D8]'>
                            <div className='font-bold second-color text-2xl md:text-4xl mb-2 md:mb-5'>0{index + 1}</div>
                            <div className='text-[#616666] text-base md:text-2xl'>{item.content}</div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default HomeGuide;
