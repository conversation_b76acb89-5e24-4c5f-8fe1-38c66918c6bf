"use client";

import '../../../../css/index.css';
import 'swiper/css';
import { useState, useEffect } from 'react';
import message from 'antd/lib/message';
import { router } from '@inertiajs/react';
import axios from 'axios';
import { initializeGSAP } from './HomeGSAP';

const HomeRecommend = () => {
    const [messageApi, contextHolder] = message.useMessage();
    const [products_list, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        console.log('here');
        
        fetchProducts();
    }, []);

    useEffect(() => {
        if (products_list.length > 0) {
            setTimeout(() => {
                initializeGSAP();
            }, 100);
        }
    }, [products_list]);

    const fetchProducts = async () => {
        try {
            setLoading(true);
            const response = await axios.get('/api/products');
            if (response.data.success) {
                setProducts(response.data.data || []); // 确保设置一个数组
            } else {
                throw new Error(response.data.message);
            }
        } catch (error) {
            console.error('Error fetching products:', error);
            messageApi.error('獲取產品資料失敗');
            setProducts([]); // 错误时设置空数组
        } finally {
            setLoading(false);
        }
    };

    // MORE INFO
    const handleAddToInfo = (item, e) => {
        e.preventDefault();
        e.stopPropagation();
        router.visit(`/product/${item.id}`);
    };

    if (loading) {
        return <div className="text-center py-8">載入中...</div>;
    }

    return (
        <div className="relative w-full">
            {contextHolder}
            <div className='product-list-container'>
                <div className="grid grid-cols-1 gap-x-8 gap-y-8 sm:grid-cols-2 lg:grid-cols-3">
                    {Array.isArray(products_list) && products_list.map((item, index) => (
                        <div key={item.id || index} className={`group rounded-2xl w-full drop-shadow-2xl bg-white ${(index + 1) % 2 === 0 ? '-mt-[0px]' : 'mt-0'}`}>
                            <a className='w-full rounded-2xl' href={`/product/${item.id}`} target="_blank">
                                <div className="overflow-hidden rounded-2xl">
                                    <img
                                        src={Array.isArray(item.images) && item.images.length > 0 
                                            // 如果沒有圖片，則顯示預設圖片
                                            ? item.images.some(image => image.url)
                                                ? item.images.find(image => image.url).url
                                                : '/default_image.png'
                                            : '/default_image.png'}
                                        alt="Banner"
                                        className='w-full'
                                        layout="responsive"
                                    />
                                </div>
                                <div className="p-4 flex flex-col gap-2">
                                    <p className="text-lg font-bold main-color">{item.title}</p>
                                    <p className="text-sm text-gray-500 line-clamp-2 min-h-[20px]">{item.specs?.detail || ''}</p>
                                    <p className="text-sm text-gray-500 min-h-[20px]">{item.specs?.size || ''}</p>
                                    <p className="mt-4 md:text-2xl text-2xl font-bold second-color text-right">$ {item.price}</p>
                                </div>
                            </a>
                            <button
                                onClick={(e) => handleAddToInfo(item, e)}
                                className="absolute bottom-4 right-4 bg-second-color text-white px-6 py-2 rounded-full 
                                         opacity-0 group-hover:opacity-100 transition-opacity duration-300
                                         hover:bg-opacity-90 z-10"
                            >
                                了解更多
                            </button>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default HomeRecommend;
