import React from 'react'
import { Link } from "react-router-dom";

function Footer() {
  

  return (
      <div className='bg-main-color p-8 mt-12'>
        <div className='flex justify-center'>
          <img
              src='/Icon/Logo_white.svg'
              alt="Banner"
              className='w-[150px] h-auto md:block hidden'
              layout="responsive"
          />
          <img
              src='/Icon/Logo_white.svg'
              alt="Banner"
              className='w-[120px] h-auto md:hidden block'
              layout="responsive"
          />
        </div>
      <div className='flex md:gap-8 gap-4 lg:flex-row flex-col-reverse'>
        <div className='w-full lg:w-2/3'>
          <ul className='md:text-base text-sm flex flex-col md:flex-row items-center md:items-start justify-between gap-4 md:gap-16 text-white lg:p-8 pb-8 flex-wrap '>
            <li>
              <Link href="/dashboard">
                <p className='font-bold'>Company</p>
              </Link>
            </li>
            <li>
              <Link href="/">
                <p className='font-bold'>Services</p>
              </Link>
            </li>
            <li>
              <Link href="/">
                <p className='font-bold'>Helpful Links</p>
              </Link>
            </li>
            <li>
              <Link href="/">
                <p className='font-bold'>Legal</p>
              </Link>
            </li>
          </ul>
        </div>
        <div className='w-full lg:w-1/3'>
          <ul className='flex md:text-base text-sm justify-center md:justify-end items-center gap-2 text-white lg:p-8 pt-8 flex-wrap '>
            <li className='mr-4 font-bold'>
              Follow Us On
            </li>
            <li>
              <Link to='https://www.instagram.com/dspworkshop/' target='_blank'>
                <img className='w-[30px] h-[30px]' src='/Icon/instagram.svg' alt='Instagram Icon'></img>
              </Link>
            </li>
            <li>
              <Link to='https://www.facebook.com/' target='_blank'>
                <img className='w-[30px] h-[30px]' src='/Icon/facebook.svg' alt='Facebook Icon'></img>
              </Link>
            </li>
          </ul>
        </div>
      </div>
      <div className='text-center text-white col-span-2 flex justify-center items-end md:text-base text-xs font-bold'>
        © {new Date().getFullYear()} Digital Speed Printing Workshop. All rights reserved.
      </div>
    </div>
  )
}

export default Footer