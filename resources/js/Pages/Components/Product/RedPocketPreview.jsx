import React, { useEffect, useRef, useState } from 'react';
import { Head } from '@inertiajs/react';
import { message } from 'antd';
import Header from '@/Pages/Components/Header/Header';
import ButtonGroup from '@/Pages/Components/FixedButtonGroup/ButtonGroup';
import Footer from '@/Pages/Components/Footer/Footer';

const RedPocketPreview = ({ clientProject, auth }) => {
    const containerRef = useRef(null);
    const [dimensions, setDimensions] = useState({
        width: 380,
        height: 194
    });
    const [currentPattern, setCurrentPattern] = useState(1);
    const [messageApi, contextHolder] = message.useMessage();
    const fabricCanvasRef = useRef(null);

    useEffect(() => {
        if (clientProject && clientProject.project_data) {
            // 设置图案
            setCurrentPattern(clientProject.product_template_id);
        }
    }, [clientProject]);

    // 监听容器尺寸变化
    useEffect(() => {
        if (!containerRef?.current) return;

        const observer = new ResizeObserver(entries => {
            for (let entry of entries) {
                const { width } = entry.contentRect;
                const scale = width / 672; // 使用与 RedPocketImageCropper 相同的基准宽度
                
                setDimensions({
                    width: Math.floor(380 * scale),
                    height: Math.floor(194 * scale)
                });

                // 如果画布已存在，更新其尺寸
                if (fabricCanvasRef.current) {
                    fabricCanvasRef.current.setDimensions({
                        width: Math.floor(380 * scale),
                        height: Math.floor(194 * scale)
                    });
                    fabricCanvasRef.current.renderAll();
                }
            }
        });

        observer.observe(containerRef.current);
        return () => observer.disconnect();
    }, []);

    return (
        <>
            <Head title="預覽" />
            <div className='layout-container'>
                {contextHolder}
                <ButtonGroup />
                <Header auth={auth} />
                <div className="mx-auto p-4">
                    <div className="max-w-2xl mx-auto">
                        <div className="mb-4">
                            <h1 className="text-lg md:text-2xl font-bold main-color mb-2">
                                {clientProject?.name}
                            </h1>
                            <p className="text-gray-600 text-sm md:text-base">
                                產品編號：{clientProject?.local_uuid}
                            </p>
                        </div>
                        <div className="mb-4 relative pocket-container" ref={containerRef}>
                            <img 
                                src={`/PacketDesign/red-pocket-pattern${currentPattern}.png`}
                                alt="背景圖片" 
                                className="w-full h-full object-cover relative" 
                            />
                            <div 
                                className="absolute"
                                style={{ 
                                    width: dimensions.width,
                                    height: dimensions.height,
                                    top: '52.5%',
                                    left: '50%',
                                    transform: 'translate(-50%, -50%)'
                                }}
                            >
                                <img 
                                    src={clientProject.project_data.image.stored_url}
                                    alt="上傳圖片"
                                    className="w-full h-full object-contain"
                                    style={{
                                        position: 'absolute',
                                        top: 0,
                                        left: 0,
                                        width: '100%',
                                        height: '100%'
                                    }}
                                />
                            </div>
                        </div>
                        {/* <div className="mt-4 space-y-4">
                            <div className="flex justify-center space-x-4">
                                <a 
                                    href={`/product/${clientProject.product_id}`}
                                    className="px-4 py-2 bg-second-color text-white rounded-lg hover:bg-opacity-90"
                                >
                                    返回編輯
                                </a>
                            </div>
                        </div> */}
                    </div>
                </div>
                <Footer />
            </div>
        </>
    );
};

export default RedPocketPreview; 