"use client";

import '../../../../css/index.css';
import { useState, useEffect } from 'react';
import { message } from 'antd';
import { router } from '@inertiajs/react';
import axios from 'axios';

const ProductList = ({ products: propProducts }) => {
    const [messageApi, contextHolder] = message.useMessage();
    const [products, setProducts] = useState(propProducts || []);
    const [loading, setLoading] = useState(!propProducts);

    useEffect(() => {
        if (!propProducts) {
            fetchProducts();
        }
    }, [propProducts]);

    // 当 props 中的 products 更新时，更新本地状态
    useEffect(() => {
        if (propProducts) {
            setProducts(propProducts);
        }
    }, [propProducts]);

    const fetchProducts = async () => {
        try {
            setLoading(true);
            const response = await axios.get('/api/products');
            if (response.data.success) {
                setProducts(response.data.data);
            } else {
                throw new Error(response.data.message);
            }
        } catch (error) {
            console.error('Error fetching products:', error);
            messageApi.error('獲取產品資料失敗');
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return <div className="text-center py-8">載入中...</div>;
    }

    return (
        <div className="grid grid-cols-1 gap-x-8 gap-y-8 sm:grid-cols-2 lg:grid-cols-3">
            {contextHolder}
            {products.map((item, index) => (
                <div key={item.id} className={`group rounded-2xl w-full drop-shadow-2xl bg-white ${(index + 1) % 2 === 0 ? '-mt-[0px]' : 'mt-0'}`}>
                    <a className='w-full rounded-2xl' href={`/product/${item.id}`}>
                        <div className="overflow-hidden rounded-2xl aspect-square">
                            <img
                                src={Array.isArray(item.images) && item.images.length > 0 
                                    ? item.images.some(image => image.url)
                                        ? item.images.find(image => image.url).url
                                        : '/default_image.png'
                                    : '/default_image.png'}
                                alt={item.title}
                                className='w-full h-full object-cover transform transition-transform duration-300 group-hover:scale-110'
                            />
                        </div>
                        <div className="p-4 flex flex-col gap-2">
                            {/* 显示标签 */}
                            {item.specs?.tag && (
                                <div className="flex gap-2 flex-wrap">
                                    {item.specs.tag.map((tag, tagIndex) => (
                                        <span 
                                            key={tagIndex}
                                            className="px-2 py-1 text-xs bg-second-color text-white rounded-full"
                                        >
                                            {tag}
                                        </span>
                                    ))}
                                </div>
                            )}
                            <p className="md:text-xl text-lg font-bold main-color transition-colors duration-300 group-hover:text-second-color">{item.title}</p>
                            <p className="text-sm text-gray-500 line-clamp-2 min-h-[20px]">{item.specs?.detail || ''}</p>
                            <p className="text-sm text-gray-500 min-h-[20px]">{item.specs?.size || ''}</p>
                            <p className="mt-4 text-2xl font-bold second-color text-right">$ {item.price}</p>
                        </div>
                    </a>
                </div>
            ))}
        </div>
    );
};

export default ProductList;
