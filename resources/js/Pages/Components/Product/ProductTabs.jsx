import React, { useState, useEffect } from 'react';
import { Radio, Tabs } from 'antd';
import ProductList from './ProductList';
import '../../../../css/index.css';
import axios from 'axios';

const ProductTabs = () => {
    const [products, setProducts] = useState([]);
    const [activeKey, setActiveKey] = useState('全部');
    
    const tags = ['全部', '新品上市', '人氣商品', '推薦商品'];

    const urlTab = new URLSearchParams(window.location.search).get('tab');

    useEffect(() => {
        fetchProducts();
        // 如果 URL 中有 tab 参数且在可用标签中，则设置为当前标签
        if (urlTab && tags.includes(urlTab)) {
            setActiveKey(urlTab);
        }
    }, [urlTab]);

    const fetchProducts = async () => {
        try {
            const response = await axios.get('/api/products');
            if (response.data.success && Array.isArray(response.data.data)) {
                setProducts(response.data.data);
            } else {
                console.error('Invalid products data structure:', response.data);
                setProducts([]);
            }
        } catch (error) {
            console.error('Error fetching products:', error);
            setProducts([]);
        }
    };

    const getFilteredProducts = (tab) => {
        if (!Array.isArray(products)) {
            console.error('Products is not an array:', products);
            return [];
        }
        if (tab === '全部') {
            return products;
        }
        return products.filter(product => 
            product.specs?.tag && 
            Array.isArray(product.specs.tag) && 
            product.specs.tag.includes(tab)
        );
    };

    const handleTabChange = (key) => {
        setActiveKey(key);
        // 更新 URL 参数
        const url = new URL(window.location);
        url.searchParams.set('tab', key);
        window.history.pushState({}, '', url);
    };
  
    return (
        <div className="product-tabs">
            <Tabs
                activeKey={activeKey}
                onChange={handleTabChange}
                className='md:p-4 p-2'
                tabPosition={'top'}
                style={{
                    height: 'auto',
                }}
                items={tags.map((tag) => ({
                    label: tag,
                    key: tag,
                    children: (
                        <div className='md:py-10 py-4 px-4 bg-[#F2F3F3]'>
                            <ProductList products={getFilteredProducts(tag)}/>
                        </div>
                    ),
                }))}
            />
        </div>
    );
};

export default ProductTabs;
