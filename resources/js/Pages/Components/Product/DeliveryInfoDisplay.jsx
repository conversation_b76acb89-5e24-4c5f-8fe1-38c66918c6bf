import React from 'react';
import { Button } from 'antd';

const DeliveryInfoDisplay = ({ 
    deliveryInfo, 
    onEdit,
    showEditButton = true,
    className = ''
}) => {
    return (
        <div className={`p-4 bg-white rounded-lg shadow ${className}`}>
            <div className="flex justify-between items-center mb-4">
                <h3 className="md:text-lg text-base font-semibold">配送資訊</h3>
                {/* <Button 
                    onClick={onEdit}
                    className="bg-second-color text-white hover:bg-opacity-90"
                >
                    {deliveryInfo ? '修改配送資訊' : '添加配送資訊'}
                </Button> */}
            </div>
            {deliveryInfo ? (
                <div className="space-y-2">
                    <p className='md:text-base text-sm'><span className="font-medium ">收件人：</span>{deliveryInfo.recipient}</p>
                    <p className='md:text-base text-sm'><span className="font-medium ">電話：</span>{deliveryInfo.phone}</p>
                    <p className='md:text-base text-sm'><span className="font-medium ">地址：</span>{deliveryInfo.address}</p>
                    {deliveryInfo.note && (
                        <p className='md:text-base text-sm'><span className="font-medium">備註：</span>{deliveryInfo.note}</p>
                    )}
                </div>
            ) : (
                <div className="text-gray-500 text-center py-4">
                    尚未添加配送資訊
                </div>
            )}
        </div>
    );
};

export default DeliveryInfoDisplay; 