import React, { useState, useEffect } from 'react';
import axios from 'axios';

export default function ProductDetail(props) {
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/products/${props.id}`);
        setProduct(response.data.data);
      } catch (err) {
        console.error('获取产品数据失败:', err);
        setError('获取产品数据失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [props.id]);

  if (loading) {
    return (
      <div className="mx-auto p-6 bg-[#F2F3F3]">
        <div className="p-8 bg-white rounded-3xl animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-8 bg-[#F2F3F3]">
        <div className="p-8 bg-white rounded-3xl">
          <p className="text-red-500">{error}</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return null;
  }
  
  return (
    <div className="mx-auto p-6 md:p-10 bg-[#F2F3F3]">
      <div className="p-8 bg-white rounded-3xl">
        <p className="md:text-3xl text-2xl font-bold main-color md:mt-12 mt-0">{product.title}</p>
        <div className="text-sm text-gray-500 font-medium mt-4 whitespace-pre-line leading-relaxed">
          {product.description}
        </div>
        <p className="text-sm text-gray-500 font-medium mt-4">{product.specs?.detail ? `產品細節：${product.specs.detail}` : ''}</p>
        <p className='text-sm text-gray-500 font-medium mt-4'>{product.specs?.page ? `頁數：${product.specs.page}` : ''}</p>
        <p className='text-sm text-gray-500 font-medium mt-4'>{product.specs?.delivery ? `送貨方式：${product.specs.delivery}` : ''}</p>
        <p className="md:text-3xl text-2xl font-bold second-color text-left mt-4">$ {product.price}</p>
        <div className='mt-4 cursor-pointer md:text-xl text-base font-bold text-white bg-main-color py-4 md:px-8 px-4 rounded-full max-w-[200px] text-center'>
          <a href={`/product/${product.id}/edit`} className='block'>立即制作</a>
        </div>
      </div>
      <div className="p-8 my-8 bg-white rounded-3xl">
        <h1 className='md:text-3xl text-2xl font-bold main-color text-center md:mb-8 mb-4 my-0'>產品圖片</h1>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
          {product.images && product.images.map((img, index) => (
            <div className='w-full h-auto' key={index}>
              <img src={img.url} alt={`${product.title} - 图片 ${index + 1}`} />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
