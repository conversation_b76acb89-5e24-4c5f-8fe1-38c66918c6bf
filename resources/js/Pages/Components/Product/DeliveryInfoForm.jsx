import React, { useEffect, useState } from 'react';
import { Form, Input, Button, message } from 'antd';
import axios from 'axios';

const DeliveryInfoForm = ({ 
    onSuccess, 
    initialValues,
    userAddresses = []
}) => {
    const [form] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    const [selectedAddress, setSelectedAddress] = useState(null);
    const [addresses, setAddresses] = useState(userAddresses);

    // 处理地址选择
    const handleAddressSelect = (address) => {
        setSelectedAddress(address);
        const formValues = {
            recipient: address.recipient,
            phone: address.phone,
            address: address.address,
            notes: address.notes || ''
        };
        form.setFieldsValue(formValues);
        onSuccess?.(formValues);
    };

    // 设置初始值
    useEffect(() => {
        if (initialValues) {
            form.setFieldsValue(initialValues);
            // 只在 userAddresses 存在时查找匹配地址
            if (userAddresses) {
                const matchingAddress = userAddresses.find(addr => 
                    addr.recipient === initialValues.recipient && 
                    addr.address === initialValues.address
                );
                if (matchingAddress) {
                    setSelectedAddress(matchingAddress);
                }
            }
        } else if (userAddresses) { // 只在 userAddresses 存在时尝试使用默认地址
            // 如果没有初始值，尝试使用默认地址
            const defaultAddress = userAddresses.find(addr => addr.is_default);
            console.log(defaultAddress,'defaultAddress');
            console.log(userAddresses,'userAddresses');
            
            if (defaultAddress) {
                handleAddressSelect(defaultAddress);
            }
        }
    }, [initialValues, userAddresses]);

    const handleSubmit = async (values) => {
        try {
            await axios.post('/user/delivery-info', values);
            messageApi.success('配送資訊已更新');
            onSuccess?.(values);
        } catch (error) {
            messageApi.error('更新失敗');
        }
    };

    return (
        <div className="p-0 bg-white rounded-lg border-none">
            {contextHolder}
            <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                onValuesChange={(_, allValues) => {
                    onSuccess?.(allValues);
                }}
            >
                {userAddresses && userAddresses.length > 0 && (
                    <Form.Item label="選擇已保存的地址">
                        <div className="flex flex-wrap gap-2 mb-4">
                            {userAddresses.map((addr, index) => (
                                <Button
                                    key={index}
                                    type={selectedAddress === addr ? 'primary' : 'default'}
                                    onClick={() => handleAddressSelect(addr)}
                                    className={selectedAddress === addr ? 'bg-second-color' : ''}
                                >
                                    {addr.name}
                                </Button>
                            ))}
                        </div>
                    </Form.Item>
                )}
                <Form.Item
                    label="收件人姓名"
                    name="recipient"
                    rules={[{ required: true, message: '請輸入收件人姓名' }]}
                >
                    <Input />
                </Form.Item>

                <Form.Item
                    label="聯絡電話"
                    name="phone"
                    rules={[{ required: true, message: '請輸入聯絡電話' }]}
                >
                    <Input />
                </Form.Item>

                <Form.Item
                    label="配送地址"
                    name="address"
                    rules={[{ required: true, message: '請輸入配送地址' }]}
                >
                    <Input.TextArea rows={3} />
                </Form.Item>

                <Form.Item
                    label="備註"
                    name="note"
                >
                    <Input.TextArea rows={2} />
                </Form.Item>
            </Form>
        </div>
    );
};

export default DeliveryInfoForm; 