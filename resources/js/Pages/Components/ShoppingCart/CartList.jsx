import React, { useState, useEffect } from 'react';
import axios from 'axios';
import message from 'antd/lib/message';
// import { Input } from "@nextui-org/react";
import { useCart } from '@/Pages/Contexts/CartContext';

export default function CartList() {
    const [cartItems, setCartItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const [messageApi, contextHolder] = message.useMessage();
    const [totalPrice, setTotalPrice] = useState(0);

    const { 
        removeFromCart,
        updateQuantity
    } = useCart();

    // 获取购物车数据
    const fetchCartItems = async () => {
        try {
            const response = await axios.get('/cart/list');
            if (response.data.success) {
                const cart = response.data.data;
                if (cart?.cart_meta) {
                    setCartItems(cart.cart_meta);
                    calculateTotal(cart.cart_meta);
                }
            }
        } catch (error) {
            console.error('获取购物车数据失败:', error);
            messageApi.error('獲取購物車數據失敗');
        } finally {
            setLoading(false);
        }
    };

    // 计算总价
    const calculateTotal = (items) => {
        const total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        setTotalPrice(total);
    };

    // 处理数量变更
    const handleCountChange = async (item, newValue) => {
        try {
            const count = parseInt(newValue);
            await axios.post('/cart/update', {
                client_project_id: item.client_project_id,
                quantity: count <= 0 ? 1 : count
            });
            await fetchCartItems();
        } catch (error) {
            console.error('更新数量失败:', error);
            messageApi.error('更新數量失敗');
        }
    };

    // 处理删除
    const handleRemove = async (clientProjectId) => {
        try {
            await axios.delete(`/cart/${clientProjectId}`);
            await fetchCartItems();
            messageApi.success('商品已移除');
        } catch (error) {
            console.error('删除失败:', error);
            messageApi.error('刪除失敗');
        }
    };

    // 处理预览
    const handlePreview = (clientProjectId, uuid) => {
        window.location.href = `/client-projects/preview/${clientProjectId}/${uuid}`;
    };

    useEffect(() => {
        fetchCartItems();
    }, []);

    if (loading) {
        return <div>Loading...</div>;
    }

    if (cartItems.length === 0) {
        return (
            <div className='px-8 flex flex-col items-center justify-center gap-8 my-16'>
                <p className="text-xl font-bold main-color">購物車是空的</p>
                <a href="/product" className='main-color bg-second-color px-10 py-2 font-bold rounded-full'>前往購物</a>
            </div>
        );
    }

    return (
        <div className='md:px-8 flex flex-col gap-8 md:my-8 my-0 px-0'>
            {/* <div className="bg-white rounded-3xl p-4 px-24 flex items-center justify-between drop-shadow-2xl">
                <div className="flex items-center gap-8">
                    <button 
                        onClick={handleClearCart}
                        className="px-4 py-1 text-sm text-red-500 border border-red-500 rounded-full hover:bg-red-50 transition-colors duration-300"
                    >
                        清空購物車
                    </button>
                </div>
                <div className="flex items-center gap-4">
                    <span className="text-lg font-bold main-color">產品總計:</span>
                    <span className="text-2xl font-bold main-color">$ {totalPrice.toFixed(2)}</span>
                </div>
            </div> */}
            
            {contextHolder}
            {cartItems.map((item) => (
                <div 
                    className="bg-white rounded-3xl min-h-[200px] md:py-8 py-4 md:px-24 px-4 flex md:flex-row flex-col items-center gap-8 drop-shadow-2xl" 
                    key={`cart-item-${item.client_project_id}`}
                >
                    <div className='w-full md:w-3/12'>
                        <img 
                            className='w-full h-auto object-contain rounded-2xl' 
                            src={item.product_image} 
                            alt={item.product_name || '商品圖片'} 
                        />
                    </div>
                    <div className='w-full md:w-9/12'>
                        <div className='flex items-center gap-2'>
                            <p className="text-lg font-bold main-color">
                                {item.product_name || '未命名商品'}
                            </p>
                            <button 
                                className='text-sm font-bold bg-second-color main-color px-2 py-1 rounded-full'
                                onClick={() => handlePreview(item.client_project_id, item.local_uuid)}
                            >
                                Preview
                            </button>
                        </div>
                        <p className="text-sm mt-2 text-gray-500">{item.local_uuid || ''}</p>
                        <p className="text-sm text-gray-500">{item.specs?.size || ''}</p>
                        <p className="text-sm text-gray-500">{item.specs?.detail || ''}</p>
                        <p className="mt-4 text-2xl font-bold second-color">
                            $ {((item.price || 0) * item.quantity).toFixed(2)}
                        </p>
                        <div className='flex items-center justify-between'>
                            <div className='w-[100px] mt-4 relative flex items-center gap-2'>
                                <input
                                    type="number"
                                    // mobile 顯示數字鍵盤
                                    inputMode="numeric"
                                    // min="0"
                                    value={item.quantity}
                                    onChange={(e) => handleCountChange(item, e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-second-color focus:border-transparent"
                                />
                                <span className="text-gray-500 text-sm">pcs</span>
                            </div>
                            <button
                                className='mt-4'
                                onClick={() => handleRemove(item.client_project_id)}
                            >
                                <img className='w-[30px] h-[30px]' src='/Icon/Trash.png' alt='Delete Icon' />
                            </button>
                        </div>
                    </div>
                    
                </div>
            ))}
            <div className="bg-white rounded-3xl p-4 md:px-24 px-4 flex items-center justify-between drop-shadow-2xl">
                {/* <div className="flex items-center gap-8">
                    <button 
                        onClick={handleClearCart}
                        className="px-4 py-1 text-sm text-red-500 border border-red-500 rounded-full hover:bg-red-50 transition-colors duration-300"
                    >
                        清空購物車
                    </button>
                </div> */}
                <div className="">
                    <span className="text-lg font-bold main-color">產品總計:</span>
                </div>
                <div className="">
                    <span className="text-2xl font-bold main-color">$ {totalPrice.toFixed(2)}</span>
                </div>
            </div>
        </div>
    );
}
