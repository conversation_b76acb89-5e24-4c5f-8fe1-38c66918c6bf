import React, { useRef, useEffect, useState } from 'react';
import { router } from '@inertiajs/react';
import axios from 'axios';

export default function CartButton({ isCartOpen, setIsCartOpen }) {
    const cartRef = useRef(false);
    const [cartCount, setCartCount] = useState(0);

    // 获取购物车数量
    const fetchCartCount = async () => {
        try {
            const response = await axios.get('/api/cart/count');
            setCartCount(response.data.count);
        } catch (error) {
            console.error('获取购物车数量失败:', error);
        }
    };

    useEffect(() => {
        fetchCartCount();
        
        // 设置定期检查购物车数量的定时器
        // const interval = setInterval(fetchCartCount, 5000);

        // return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (cartRef.current && !cartRef.current.contains(event.target)) {
                setIsCartOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [setIsCartOpen]);

    const toggleCart = () => {
        router.visit('/cart');
    }

    return (
        <div ref={cartRef}>
            <button 
                className="relative w-[50px] h-[50px] p-2.5 bg-second-color rounded-full flex items-center justify-center text-white drop-shadow-2xl transition-all duration-300 hover:p-2"
                onClick={toggleCart} 
            >
                <img className='w-fill h-auto' src="/Icon/Pay.png" alt="Toggle Cart" />
                {cartCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                        {cartCount}
                    </span>
                )}
            </button>
            {location.pathname !== '/cart' && 
            <div className={`absolute bottom-[-10px] p-4 bg-white rounded-full w-[180px] h-[65px] transition-all duration-300 flex items-center justify-center gap-5 shadow-2xl ${
                isCartOpen ? 'right-[-20px]' : 'right-[-430px]'
            }`}>
                <div className='flex items-center justify-center'>
                    <a href="/cart" className='second-color bg-main-color px-6 py-2 font-bold rounded-full'>
                        購物車 ({cartCount})
                    </a>
                </div>
            </div>}
        </div>
    )
}
