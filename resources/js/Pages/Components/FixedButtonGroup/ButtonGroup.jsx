import React, { useState } from 'react'
import { useLocation } from 'react-router-dom';
import BacktoTop from './BacktoTop'
import CartButton from './CartButton'

export default function ButtonGroup() {
  const [isCartOpen, setIsCartOpen] = useState(false);
  const location = useLocation();

  return (
    <div className='fixed md:bottom-10 md:right-10 right-2 bottom-4 z-10'>
        <BacktoTop />
        <CartButton 
          isCartOpen={isCartOpen} 
          setIsCartOpen={setIsCartOpen} 
        />
    </div>
  )
}
