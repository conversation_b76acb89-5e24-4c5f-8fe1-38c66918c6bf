import React, { useState, useEffect } from 'react'

export default function BacktoTop() {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        const toggleVisibility = () => {
            if (window.scrollY > 0) {
                setIsVisible(true);
            } else {
                setIsVisible(false);
            }
        };
        window.addEventListener('scroll', toggleVisibility);
        return () => {
            window.removeEventListener('scroll', toggleVisibility);
        };
    }, []);

    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    return (
        <button 
            className={`w-[50px] h-[50px] mb-4 p-3 bg-second-color rounded-full flex items-center justify-center text-white drop-shadow-2xl hover:drop-shadow-2xl transition-all duration-300 hover:p-2.5 ${
                isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'
            }`} 
            onClick={scrollToTop}
        >
            <img className='w-fill h-auto' src="/Icon/back-to-top.svg" alt="Back to Top" />
        </button>
    )
}
