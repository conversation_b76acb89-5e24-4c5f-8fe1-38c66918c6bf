import React, { useState } from 'react';
import { Modal, Form, Input, Space, Button, Table, message } from 'antd';
import { router } from '@inertiajs/react';

const UserDeliveryInfo = ({ isModalOpen, handleCancel, user }) => {
  const [form] = Form.useForm();
  const [editingAddress, setEditingAddress] = useState(null);

  // 安全地解析地址数据
  const parseAddresses = () => {
    try {
      if (!user?.address) return [];
      
      // 如果已经是数组，直接使用
      if (Array.isArray(user.address)) {
        return user.address.map((addr, index) => ({
          ...addr,
          key: `address-${index}`,
          is_default: addr.is_default || false
        }));
      }
      
      // 尝试解析 JSON 字符串
      const parsedAddresses = JSON.parse(user.address);
      return Array.isArray(parsedAddresses) ? parsedAddresses.map((addr, index) => ({
        ...addr,
        key: `address-${index}`,
        is_default: addr.is_default || false
      })) : [];
    } catch (error) {
      console.error('解析地址数据失败:', error);
      return [];
    }
  };

  const addresses = parseAddresses();

  const handleSubmit = (values) => {
    const newAddresses = [...addresses];
    
    if (editingAddress !== null) {
      newAddresses[editingAddress] = {
        ...values,
        key: addresses[editingAddress].key,
        is_default: addresses[editingAddress].is_default
      };
    } else {
      newAddresses.push({
        ...values,
        key: `address-${addresses.length}`,
        is_default: false
      });
    }

    const addressesToSave = newAddresses.map(({ key, ...rest }) => rest);

    router.post('/user/address', {
      address: JSON.stringify(addressesToSave)
    }, {
      onSuccess: () => {
        message.success(editingAddress !== null ? '地址更新成功' : '地址添加成功');
        handleCancel();
        setEditingAddress(null);
        form.resetFields();
      },
      onError: () => {
        message.error('操作失敗');
      }
    });
  };

  const handleSetDefault = (index) => {
    const newAddresses = addresses.map((addr, i) => ({
      ...addr,
      is_default: i === index
    }));

    const addressesToSave = newAddresses.map(({ key, ...rest }) => rest);

    router.post('/user/address', {
      address: JSON.stringify(addressesToSave)
    }, {
      onSuccess: () => {
        message.success('設置默認地址成功');
      },
      onError: () => {
        message.error('設置默認地址失敗');
      }
    });
  };

  const handleEdit = (record, index) => {
    setEditingAddress(index);
    form.setFieldsValue(record);
  };

  const handleDelete = (index) => {
    const newAddresses = addresses.filter((_, i) => i !== index);
    const addressesToSave = newAddresses.map(({ key, ...rest }) => rest);
    
    router.post('/user/address', {
      address: JSON.stringify(addressesToSave)
    }, {
      onSuccess: () => {
        message.success('地址刪除成功');
      },
      onError: () => {
        message.error('刪除失敗');
      }
    });
  };

  const handleAddNew = () => {
    setEditingAddress(null);
    form.resetFields();
  };

  const columns = [
    {
        title: '記錄名稱',
        dataIndex: 'name',
        key: 'name',
    },
    {
      title: '收件人',
      dataIndex: 'recipient',
      key: 'recipient',
    },
    {
      title: '聯絡電話',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '送貨地址',
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record, index) => (
        <Space>
          <Button
            type={record.is_default ? 'primary' : 'default'}
            size="small"
            onClick={() => handleSetDefault(index)}
            disabled={addresses.length === 1}
          >
            {record.is_default ? '默認地址' : '設為默認'}
          </Button>
          <Button 
            type="link" 
            onClick={() => handleEdit(record, index)}
          >
            編輯
          </Button>
          <Button 
            type="link" 
            danger 
            onClick={() => handleDelete(index)}
            disabled={addresses.length === 1 || record.is_default}
          >
            刪除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Modal
      title="送貨地址管理"
      open={isModalOpen}
      onCancel={() => {
        handleCancel();
        setEditingAddress(null);
        form.resetFields();
      }}
      footer={null}
      width="100%"
    >
      <div className="mb-4">
        <Button type="primary" onClick={handleAddNew}>
          新增地址
        </Button>
      </div>

      {addresses.length > 0 ? (
        <Table
          dataSource={addresses}
          columns={columns}
          rowKey="key"
          pagination={false}
          className="mb-4"
        />
      ) : (
        <div className="text-center text-gray-500 my-4">
          暫無保存的地址
        </div>
      )}

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          label="記錄名稱"
          name="name"
          rules={[{ required: true, message: '請輸入記錄名稱' }]}
        >
          <Input placeholder="請輸入記錄名稱" />
        </Form.Item>
        <Form.Item
          label="收件人"
          name="recipient"
          rules={[{ required: true, message: '請輸入收件人姓名' }]}
        >
          <Input placeholder="請輸入收件人姓名" />
        </Form.Item>

        <Form.Item
          label="聯絡電話"
          name="phone"
          rules={[{ required: true, message: '請輸入聯絡電話' }]}
        >
          <Input placeholder="請輸入聯絡電話" />
        </Form.Item>

        <Form.Item
          label="送貨地址"
          name="address"
          rules={[{ required: true, message: '請輸入送貨地址' }]}
        >
          <Input.TextArea placeholder="請輸入完整送貨地址" rows={4} />
        </Form.Item>

        <Form.Item className="mb-0 text-right">
          <Space>
            <Button onClick={() => {
              setEditingAddress(null);
              form.resetFields();
            }}>
              取消
            </Button>
            <Button type="primary" htmlType="submit">
              {editingAddress !== null ? '更新' : '保存'}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UserDeliveryInfo;