import React from 'react';
import { Button, Checkbox, Form, Input, Flex } from 'antd';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { router } from '@inertiajs/react';
import axios from 'axios';
const onFinish = async (values) => {
    let res = await axios.post('/user/login', values);
    if(res.data.status === 200) {
        router.visit('/user/profile');
    }
};
export default function LoginForm() {
    return (
        <div className='mt-8'>
            <Form
                name="basic"
                initialValues={{ remember: true }}
                onFinish={onFinish}
            >
                <Form.Item
                    className='mt-12 mb-8'
                    name="username"
                    rules={[{ required: true, message: 'Please input your Username or Email!' }]}
                >
                    <Input prefix={<UserOutlined />} placeholder="Username Or Email" />
                </Form.Item>
                <Form.Item
                    className='mb-4'
                    name="password"
                    rules={[{ required: true, message: 'Please input your Password!' }]}
                >
                    <Input prefix={<LockOutlined />} type="password" placeholder="Password" />
                </Form.Item>
                <Form.Item>
                    <Flex justify="space-between" align="center">
                        <Form.Item name="remember" valuePropName="checked" noStyle>
                            <Checkbox>Remember me</Checkbox>
                        </Form.Item>
                        <a href="">Forgot password</a>
                    </Flex>
                </Form.Item>
                <Form.Item>
                    <Button block type="primary" htmlType="submit">
                        Log in
                    </Button>
                    <div className='mt-4'>
                        <a href="/user/register">Sign in now!</a>
                    </div>
                </Form.Item>
            </Form>
        </div>
    );
}