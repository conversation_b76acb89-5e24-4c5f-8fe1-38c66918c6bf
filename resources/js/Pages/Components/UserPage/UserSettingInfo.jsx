import React, { useEffect } from 'react';
import { Modal, Form, Input, Button, Tabs, message } from 'antd';
import { router } from '@inertiajs/react';

const UserSettingInfo = ({ isModalOpen, handleCancel, user }) => {
  const [basicForm] = Form.useForm();
  const [passwordForm] = Form.useForm();

  // 当用户信息改变时，更新表单
  useEffect(() => {
    if (user) {
      basicForm.setFieldsValue({
        name: user.name,
        email: user.email,
        phone: user.phone,
      });
    }
  }, [user]);

  // 处理模态框关闭
  const handleModalCancel = () => {
    basicForm.resetFields();
    passwordForm.resetFields();
    handleCancel();
  };

  // 处理基本信息更新
  const handleBasicInfoSubmit = (values) => {
    router.post('/user/profile/update', values, {
      onSuccess: () => {
        message.success('基本資料更新成功');
      },
      onError: () => {
        message.error('更新失敗');
      }
    });
  };

  // 处理密码更新
  const handlePasswordSubmit = (values) => {
    router.post('/user/password/update', values, {
      onSuccess: () => {
        message.success('密碼更新成功');
        passwordForm.resetFields();
      },
      onError: () => {
        message.error('密碼更新失敗');
      }
    });
  };

  const items = [
    {
      key: '1',
      label: '基本資料',
      children: (
        <Form
          form={basicForm}
          layout="vertical"
          onFinish={handleBasicInfoSubmit}
          initialValues={{
            name: user?.name || '',
            email: user?.email || '',
            phone: user?.phone || '',
          }}
        >
          <Form.Item
            label="用戶名稱"
            name="name"
            rules={[{ required: true, message: '請輸入用戶名稱' }]}
          >
            <Input placeholder="請輸入用戶名稱" />
          </Form.Item>

          <Form.Item
            label="電子郵件"
            name="email"
            rules={[
              { required: true, message: '請輸入電子郵件' },
              { type: 'email', message: '請輸入有效的電子郵件' }
            ]}
          >
            <Input placeholder="請輸入電子郵件" />
          </Form.Item>

          <Form.Item
            label="聯絡電話"
            name="phone"
            rules={[{ required: true, message: '請輸入聯絡電話' }]}
          >
            <Input placeholder="請輸入聯絡電話" />
          </Form.Item>

          <Form.Item className="mb-0 text-right">
            <Button type="primary" htmlType="submit">
              更新資料
            </Button>
          </Form.Item>
        </Form>
      ),
    },
    {
      key: '2',
      label: '修改密碼',
      children: (
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handlePasswordSubmit}
        >
          <Form.Item
            label="當前密碼"
            name="old_password"
            rules={[{ required: true, message: '請輸入當前密碼' }]}
          >
            <Input.Password placeholder="請輸入當前密碼" />
          </Form.Item>

          <Form.Item
            label="新密碼"
            name="new_password"
            rules={[
              { required: true, message: '請輸入新密碼' },
              { min: 8, message: '密碼長度至少為8個字符' }
            ]}
          >
            <Input.Password placeholder="請輸入新密碼" />
          </Form.Item>

          <Form.Item
            label="確認新密碼"
            name="confirm_password"
            dependencies={['new_password']}
            rules={[
              { required: true, message: '請確認新密碼' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('new_password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('兩次輸入的密碼不一致'));
                },
              }),
            ]}
          >
            <Input.Password placeholder="請再次輸入新密碼" />
          </Form.Item>

          <Form.Item className="mb-0 text-right">
            <Button type="primary" htmlType="submit">
              更新密碼
            </Button>
          </Form.Item>
        </Form>
      ),
    },
  ];

  return (
    <Modal
      title="用戶設置"
      open={isModalOpen}
      onCancel={handleModalCancel}
      footer={null}
      width={800}
    >
      <Tabs
        defaultActiveKey="1"
        items={items}
        className="mt-4"
      />
    </Modal>
  );
};

export default UserSettingInfo;