import React, { useRef, useState } from 'react';
import { Avatar, Button, message } from 'antd';
import { router } from '@inertiajs/react';
import UserDeliveryInfo from './UserDeliveryInfo';
import UserSettingInfo from './UserSettingInfo';
export default function UserAvatar({ user }) {
  const fileInputRef = useRef(null);
  const [timestamp, setTimestamp] = useState(Date.now());
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSettingModalOpen, setIsSettingModalOpen] = useState(false);
  // 处理地址模态框
  const showDeliveryModal = () => {
    setIsModalOpen(true);
  };

  const showSettingModal = () => {
    setIsSettingModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setIsSettingModalOpen(false);
  };

  // 处理登出
  const handleLogout = () => {
    // 使用 window.location.href 直接跳转
    window.location.href = '/user/logout';
  };

  // 处理头像点击
  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  // 处理头像上传
  const handleAvatarUpload = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      message.error('請上傳圖片文件');
      return;
    }

    // 验证文件大小（例如：2MB）
    if (file.size > 2 * 1024 * 1024) {
      message.error('圖片大小不能超過 2MB');
      return;
    }

    const formData = new FormData();
    formData.append('avatar', file);

    try {
      router.post('/user/avatar', formData, {
        forceFormData: true,
        preserveState: true,
        onSuccess: () => {
          // 更新时间戳来强制重新加载图片
          setTimestamp(Date.now());
        },
      });
    } catch (error) {
      console.error('上傳失敗:', error);
    }
  };

  return (
    <div className='flex items-end flex-col p-4 bg-white rounded-2xl drop-shadow-3xl md:m-10 m-6'>
      <div className='relative flex gap-4 items-left justify-between w-full flex-col pr-24'>
          <div className='flex items-center gap-2'>
            <div className="cursor-pointer relative group" onClick={handleAvatarClick}>
              <Avatar 
                className='w-[40px] md:w-[60px] h-auto bg-white drop-shadow-3xl transition-opacity duration-300 group-hover:opacity-80' 
                src={`${user?.avatar || '/admin-assets/default-avatar.png'}?t=${timestamp}`}
                alt="avatar" 
              />
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full">
                更換頭像
              </div>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              style={{ display: 'none' }}
              onChange={handleAvatarUpload}
            />
            <div style={{width:'calc(100% - 30px)'}}>
              <p>{user?.name || '未登录'}</p>
            </div>
          </div>
          <div className='absolute right-4 top-1/2 -translate-y-1/2 flex items-center gap-8'>
            <button 
              className='w-[28px] md:w-[20px]'
              onClick={showDeliveryModal}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-7">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418" />
              </svg>
            </button>
            <button 
              className='w-[28px] md:w-[20px]'
              onClick={showSettingModal}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-7">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z" />
                <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
              </svg>

            </button>
          </div>
          <Button 
            onClick={handleLogout} 
            type="primary" 
            className='max-w-[100px] mt-0 flex-1'
          >
            登出
          </Button>
      </div>

      <UserDeliveryInfo 
        isModalOpen={isModalOpen}
        handleCancel={handleCancel}
        user={user}
      />

      <UserSettingInfo 
        isModalOpen={isSettingModalOpen}
        handleCancel={handleCancel}
        user={user}
      />
    </div>
  )
}
