import React from 'react';
import { Button, Checkbox, Form, Input, Flex } from 'antd';
import { LockOutlined, UserOutlined, MailOutlined } from '@ant-design/icons';
import { router } from '@inertiajs/react';
import axios from 'axios';
const onFinish = async (values) => {
    console.log('Success:', values);
    let res = await axios.post('/user/register', values);
    if(res.data.status === 200) {
        router.visit('/user/profile');
    }
    else {
        console.log(res.data);
    }
};
export default function LoginForm() {
    return (
        <div className='mt-8'>
            <Form
                name="basic"
                initialValues={{ remember: true }}
                onFinish={onFinish}
            >
                <Form.Item
                    className='mt-12 mb-6'
                    name="email"
                    rules={[{ required: true, message: 'Please input your Email!',type: 'email'  }]}
                >
                    <Input prefix={<MailOutlined />} placeholder="Email" />
                </Form.Item>
                <Form.Item
                    className='mb-6'
                    name="name"
                    rules={[{ required: true, message: 'Please input your Name!' }]}
                >
                    <Input prefix={<UserOutlined />} placeholder="Username" />
                </Form.Item>
                <Form.Item
                    className='mb-12'
                    name="password"
                    rules={[{ required: true, message: 'Please input your Password!' }]}
                >
                    <Input prefix={<LockOutlined />} type="password" placeholder="Password" />
                </Form.Item>
                <Form.Item>
                    <Button block type="primary" htmlType="submit">
                        Sign in
                    </Button>
                    <div className='mt-4'>
                        <a href="/user/login">Login now</a>
                    </div>
                </Form.Item>
            </Form>
        </div>
    );
}