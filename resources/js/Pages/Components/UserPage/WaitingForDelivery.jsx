import React from 'react';
import axios from 'axios';
import { message } from 'antd';

const WaitingForDelivery = ({ orders, auth }) => {
    const [messageApi, contextHolder] = message.useMessage();

    const handleConfirmDelivery = async (orderId) => {
        try {
            console.log(orderId);
            const response = await axios.post(`/orders/confirm-delivery/${orderId}`, {
                order_id: orderId,
            });

            if (response.data.success) {
                messageApi.success('訂單已完成');
                // 刷新页面或更新订单列表
                window.location.reload();
            } else {
                throw new Error(response.data.message);
            }
        } catch (error) {
            console.error('確認收貨失敗:', error);
            messageApi.error(error.response?.data?.message || '確認收貨失敗，請稍後重試');
        }
    };

    const handleViewInvoice = (uuid) => {
        console.log(uuid);
        window.open(`/thankyou/${uuid}`, '_blank');
    }
    return (
        <>
            {contextHolder}
                    <div className="space-y-6">
                        {orders.length === 0 ? (
                            <div className="text-center py-8">
                                <p className="text-gray-500">暫無待收貨訂單</p>
                            </div>
                        ) : (
                            orders.map((order) => (
                                <div key={order.id} className="bg-white rounded-lg shadow-md p-6">
                                    <div className="flex justify-between items-start mb-4">
                                        <div>
                                            <h2 className="text-lg font-semibold main-color">
                                                訂單編號: {order.order_no}
                                            </h2>
                                            <p className="text-sm text-gray-500">
                                                下單時間: {new Date(order.order_date).toLocaleString()}
                                            </p>
                                        </div>
                                        <span className="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-full">
                                            處理中
                                        </span>
                                    </div>
                                    
                                    <div className="space-y-4">
                                        {order.product_list.map((product, index) => (
                                            <div key={index} className="flex items-center gap-4 border-b pb-4">
                                                <img 
                                                    src={product.image.url} 
                                                    alt={product.title}
                                                    className="w-20 h-20 object-cover rounded-md"
                                                />
                                                <div className="flex-1">
                                                    <h3 className="font-medium">{product.title}</h3>
                                                    <p className="text-sm text-gray-500">
                                                        數量: {product.quantity}
                                                    </p>
                                                    <p className="text-sm second-color">
                                                        單價: ${product.price}
                                                    </p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                    
                                    <div className="mt-4 flex justify-between items-center">
                                        <div>
                                            <p className="text-sm text-gray-600">
                                                總金額: <span className="font-bold second-color">${order.total_price}</span>
                                            </p>
                                        </div>
                                        <div className="space-x-4">
                                            <button onClick={() => handleViewInvoice(order.invoice.uuid)}
                                                className="px-4 py-2 bg-second-color text-white rounded-lg hover:bg-opacity-90"
                                            >
                                                查看發票
                                            </button>
                                            <button onClick={() => handleConfirmDelivery(order.id)}
                                                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                                            >
                                                確認收貨
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
        </>
    );
};

export default WaitingForDelivery;
