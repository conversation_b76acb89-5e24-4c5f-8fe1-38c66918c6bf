import React from 'react'
import { Link } from '@inertiajs/react'
export default function UserOrder() {
  return (
    <div>
        <div className='bg-white rounded-2xl md:p-8 p-4 md:m-10 m-6'>
            <p className='md:text-2xl text-xl font-bold main-color'>我的訂單</p>
            <div className='flex items-center gap-8 w-full justify-center my-8'>
                {/* <button className='flex flex-col items-center gap-2 group transition-all'>
                    <svg xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="32" height="32" viewBox="0 0 32 32"><g><g><rect className='group-hover:stroke-[#008891] transition-all' x="1" y="1" width="30" height="30" rx="9" fillOpacity="0" strokeOpacity="1" stroke="#616666" fill="none" strokeWidth="2"/></g><g><g><ellipse className='group-hover:fill-[#DEDC00] transition-all' cx="10" cy="16" rx="2" ry="2" fill="#C9C9C9" fillOpacity="1"/></g><g><ellipse className='group-hover:fill-[#DEDC00] transition-all' cx="16" cy="16" rx="2" ry="2" fill="#C9C9C9" fillOpacity="1"/></g><g><ellipse className='group-hover:fill-[#DEDC00] transition-all' cx="22" cy="16" rx="2" ry="2" fill="#C9C9C9" fillOpacity="1"/></g></g></g></svg>
                    <p className='transition-all group-hover:text-[#008891] font-bold'></p>
                </button> */}
                <Link href="/user-order-info/waiting-for-payment">
                    <button className='flex flex-col items-center gap-2 group'>
                        <svg className='transition-all' xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="32" height="32" viewBox="0 0 32 32"><g><g><rect className='group-hover:stroke-[#008891]' x="1" y="1" width="30" height="30" rx="9" fillOpacity="0" strokeOpacity="1" stroke="#616666" fill="none" strokeWidth="2"/></g><g><path className='transition-all group-hover:fill-[#DEDC00]' d="M19.887999999999998,18.562Q19.887999999999998,19.426000000000002,19.456,20.209Q19.024,20.992,18.186999999999998,21.514Q17.35,22.036,16.18,22.108L16.18,23.566000000000003L15.208,23.566000000000003L15.208,22.108Q13.57,21.964,12.544,21.055Q11.518,20.146,11.5,18.742L13.246,18.742Q13.318,19.497999999999998,13.813,20.055999999999997Q14.308,20.614,15.208,20.758L15.208,16.258Q14.001999999999999,15.952,13.264,15.628Q12.526,15.304,12.004,14.620000000000001Q11.482,13.936,11.482,12.784Q11.482,11.326,12.499,10.372001000000001Q13.516,9.418,15.208,9.31L15.208,7.816L16.18,7.816L16.18,9.31Q17.71,9.436,18.646,10.291Q19.582,11.146001,19.726,12.514L17.98,12.514Q17.89,11.884001,17.422,11.371Q16.954,10.858001,16.18,10.696L16.18,15.088000000000001Q17.368000000000002,15.394,18.115000000000002,15.709Q18.862000000000002,16.024,19.375,16.708Q19.887999999999998,17.392,19.887999999999998,18.562ZM13.156,12.693999999999999Q13.156,13.576,13.678,14.044Q14.2,14.512,15.208,14.818L15.208,10.660001Q14.272,10.750001,13.714,11.263Q13.156,11.776,13.156,12.693999999999999ZM16.18,20.776Q17.152,20.668,17.701,20.073999999999998Q18.25,19.48,18.25,18.652Q18.25,17.77,17.71,17.293Q17.17,16.816,16.18,16.528L16.18,20.776Z" fill="#C9C9C9" fillOpacity="1"/></g></g></svg>
                        <p className='transition-all group-hover:text-[#008891] font-bold md:text-base text-sm'>待支付訂單</p>
                    </button>
                </Link>
                <Link href="/user-order-info/waiting-for-delivery">
                    <button className='flex flex-col items-center gap-2 group transition-all'>
                        <svg xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="33" height="33" viewBox="0 0 33 33"><g><g><path className='group-hover:fill-[#DEDC00]' d="M8.79981506640625,15.54225515625L8.79961606640625,15.54219515625Q8.65903706640625,15.50002515625,8.51226806640625,15.50002515625Q8.41377666640625,15.50002515625,8.31717806640625,15.51923515625Q8.22057906640625,15.53845515625,8.12958506640625,15.57614515625Q8.03859006640625,15.61383515625,7.95669806640625,15.668555156250001Q7.87480506640625,15.72327515625,7.80516106640625,15.79291515625Q7.73551706640625,15.86255515625,7.68079806640625,15.944455156250001Q7.62608006640625,16.02634515625,7.58838906640625,16.11733515625Q7.55069806640625,16.20833515625,7.53148306640625,16.30493515625Q7.51226806640625,16.40153515625,7.51226806640625,16.50002515625Q7.51226806640625,16.58015515625,7.52502806640625,16.65925515625Q7.53778706640625,16.73836515625,7.56298106640625,16.81443515625Q7.58817406640625,16.89049515625,7.62515906640625,16.96158515625Q7.66214306640625,17.03266515625,7.70997506640625,17.09695515625Q7.75780706640625,17.16124515625,7.8152650664062495,17.21709515625Q7.87272406640625,17.27294515625,7.93834206640625,17.31893515625Q8.00396106640625,17.36491515625,8.07606606640625,17.39987515625Q8.14817006640625,17.43482515625,8.22492006640625,17.457845156250002Q12.76292806640625,18.81925515625,13.58378806640625,20.871415156250002L14.20136806640625,22.41538515625L15.27559806640625,21.14603515625Q19.22726806640625,16.47669515625,21.89366806640625,14.41630515625Q24.217668066406247,12.62045815625,25.19046806640625,12.94685515625L25.19216806640625,12.94742515625Q25.34796806640625,13.00002515625,25.51226806640625,13.00002515625Q25.61076806640625,13.00002515625,25.70736806640625,12.98080515625Q25.80396806640625,12.96159515625,25.89496806640625,12.923905156250001Q25.98596806640625,12.88621115625,26.06786806640625,12.83149215625Q26.14976806640625,12.77677315625,26.21936806640625,12.70713015625Q26.28896806640625,12.63748615625,26.34376806640625,12.55559315625Q26.39846806640625,12.47370015625,26.43616806640625,12.38270615625Q26.47386806640625,12.29171215625,26.49306806640625,12.19511315625Q26.51226806640625,12.09851415625,26.51226806640625,12.00002285625Q26.51226806640625,11.92205225625,26.50016806640625,11.84502395625Q26.48806806640625,11.76799515625,26.46416806640625,11.69377115625Q26.44036806640625,11.61954715625,26.40526806640625,11.54992115625Q26.37016806640625,11.48029515625,26.32466806640625,11.41695015625Q26.27926806640625,11.35360515625,26.22446806640625,11.29807315625Q26.16976806640625,11.24254015625,26.10706806640625,11.19616215625Q26.04436806640625,11.14978315625,25.97526806640625,11.11368115625Q25.90616806640625,11.07757815625,25.83236806640625,11.05262315625Q23.84466806640625,10.38115515625,20.67076806640625,12.83373815625Q18.15310806640625,14.77918515625,14.64509806640625,18.80950515625Q12.972078066406251,16.794105156249998,8.80054406640625,15.54247515625L8.79981506640625,15.54225515625Z" fillRule="evenodd" fill="#C9C9C9" fillOpacity="1"/></g><g><path d="M31,16.5Q31,22.5061,26.753,26.753Q26.6124,26.8937,26.5363,27.0775Q26.4602,27.2612,26.4602,27.4602Q26.4602,27.5586,26.4794,27.6552Q26.4986,27.7518,26.5363,27.8428Q26.574,27.9338,26.6287,28.0157Q26.6834,28.0976,26.753,28.1673Q26.8227,28.2369,26.9046,28.2916Q26.9865,28.3463,27.0775,28.384Q27.1685,28.4217,27.2651,28.4409Q27.3617,28.4602,27.4602,28.4602Q27.6591,28.4602,27.8428,28.384Q28.0266,28.3079,28.1673,28.1673Q33,23.3345,33,16.5Q33,9.66547,28.1673,4.832739999999999Q23.3345,0,16.5,0Q9.66548,0,4.832739999999999,4.832739999999999Q0,9.66548,0,16.5Q0,23.3345,4.832739999999999,28.1673Q9.66547,33,16.5,33Q16.5985,33,16.6951,32.9808Q16.7917,32.961600000000004,16.8827,32.9239Q16.9737,32.8862,17.0556,32.8315Q17.1375,32.776700000000005,17.2071,32.7071Q17.2767,32.6375,17.3315,32.5556Q17.3862,32.4737,17.4239,32.3827Q17.4616,32.2917,17.4808,32.1951Q17.5,32.0985,17.5,32Q17.5,31.9015,17.4808,31.8049Q17.4616,31.7083,17.4239,31.6173Q17.3862,31.5263,17.3315,31.4444Q17.2767,31.3625,17.2071,31.2929Q17.1375,31.2232,17.0556,31.1685Q16.9737,31.1138,16.8827,31.0761Q16.7917,31.0384,16.6951,31.0192Q16.5985,31,16.5,31Q10.4939,31,6.24695,26.753Q2,22.5061,2,16.5Q2,10.4939,6.24695,6.24695Q10.4939,2,16.5,2Q22.5061,2,26.753,6.24695Q31,10.4939,31,16.5Z" fillRule="evenodd" className='group-hover:fill-[#008891]' fill="#616666" fillOpacity="1"/></g><g><rect className='group-hover:fill-[#DEDC00]' x="21" y="28" width="4" height="4" rx="2" fill="#C9C9C9" fillOpacity="1"/></g></g></svg>
                        <p className='transition-all group-hover:text-[#008891] font-bold md:text-base text-sm'>待收貨</p>
                    </button>
                </Link>
                <Link href="/user-order-info/payment-history">
                    <button className='flex flex-col items-center gap-2 group'>
                        <svg xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="37.5" height="34" viewBox="0 0 37.5 34"><g><g><path d="M17,34Q24.0416,34,29.0208,29.0208Q29.1615,28.8802,29.2376,28.6964Q29.3137,28.5126,29.3137,28.3137Q29.3137,28.2152,29.2945,28.1186Q29.2753,28.022,29.2376,27.931Q29.1999,27.84,29.1452,27.7581Q29.0905,27.6762,29.0208,27.6066Q28.9512,27.537,28.8693,27.4822Q28.7874,27.4275,28.6964,27.3898Q28.6054,27.3521,28.5088,27.3329Q28.4122,27.3137,28.3137,27.3137Q28.1148,27.3137,27.931,27.3898Q27.7473,27.466,27.6066,27.6066L27.6054,27.6078Q23.2123,32,17,32Q10.78679,32,6.3934,27.6066Q2,23.2132,2,17Q2,10.7868,6.3934,6.3934Q10.78679,2,17,2Q23.2132,2,27.6066,6.3934Q31.4016,10.18839,31.9185,15.3412L28.4472,13.6056Q28.2361,13.5,28,13.5Q27.9015,13.5,27.8049,13.5192Q27.7083,13.5384,27.6173,13.5761Q27.5263,13.6138,27.4444,13.6685Q27.3625,13.7232,27.2929,13.7929Q27.2232,13.8625,27.1685,13.9444Q27.1138,14.0263,27.0761,14.1173Q27.0384,14.2083,27.0192,14.3049Q27,14.4015,27,14.5Q27,14.6393,27.0381,14.7733Q27.0761,14.9072,27.1494,15.0257Q27.2226,15.1442,27.3254,15.2382Q27.4282,15.3321,27.5528,15.3944L33.3245,18.2803L37.3192,12.5735Q37.4075,12.4474,37.4537,12.3006Q37.5,12.1539,37.5,12Q37.5,11.9015,37.4808,11.8049Q37.4616,11.7083,37.4239,11.6173Q37.3862,11.5263,37.3315,11.4444Q37.2767,11.3625,37.2071,11.2929Q37.1375,11.2232,37.0556,11.1685Q36.9737,11.1138,36.8827,11.0761Q36.7917,11.0384,36.6951,11.0192Q36.5985,11,36.5,11Q36.3794,11,36.2623,11.0287Q36.1451,11.0573,36.0382,11.113Q35.9312,11.1687,35.8406,11.2482Q35.7499,11.3277,35.6808,11.4265L35.6794,11.4285L33.7814,14.1399Q32.9568,8.91515,29.0208,4.9791799999999995Q24.0416,0,17,0.0000029999999999752447Q9.95837,0,4.9791799999999995,4.97919Q0,9.95837,0.0000029999999999752447,17Q0,24.0416,4.97919,29.0208Q9.95836,34,17,34Z" className='transition-all group-hover:fill-[#008891]' fillRule="evenodd" fill="#616666" fillOpacity="1"/></g><g><path d="M20.887999999999998,18.562Q20.887999999999998,19.426000000000002,20.456,20.209Q20.024,20.992,19.186999999999998,21.514Q18.35,22.036,17.18,22.108L17.18,23.566000000000003L16.208,23.566000000000003L16.208,22.108Q14.57,21.964,13.544,21.055Q12.518,20.146,12.5,18.742L14.246,18.742Q14.318,19.497999999999998,14.812999999999999,20.055999999999997Q15.308,20.614,16.208,20.758L16.208,16.258Q15.001999999999999,15.952,14.264,15.628Q13.526,15.304,13.004,14.620000000000001Q12.482,13.936,12.482,12.784Q12.482,11.326,13.498999999999999,10.372001000000001Q14.516,9.418,16.208,9.31L16.208,7.816L17.18,7.816L17.18,9.31Q18.71,9.436,19.646,10.291Q20.582,11.146001,20.726,12.514L18.98,12.514Q18.89,11.884001,18.422,11.371Q17.954,10.858001,17.18,10.696L17.18,15.088000000000001Q18.368000000000002,15.394,19.115000000000002,15.709Q19.862000000000002,16.024,20.375,16.708Q20.887999999999998,17.392,20.887999999999998,18.562ZM14.155999999999999,12.693999999999999Q14.155999999999999,13.576,14.678,14.044Q15.2,14.512,16.208,14.818L16.208,10.660001Q15.272,10.750001,14.714,11.263Q14.155999999999999,11.776,14.155999999999999,12.693999999999999ZM17.18,20.776Q18.152,20.668,18.701,20.073999999999998Q19.25,19.48,19.25,18.652Q19.25,17.77,18.71,17.293Q18.17,16.816,17.18,16.528L17.18,20.776Z" className="transition-all group-hover:fill-[#DEDC00]" fill="#C9C9C9" fillOpacity="1"/></g></g></svg>
                        <p className='transition-all group-hover:text-[#008891] font-bold md:text-base text-sm'>過往紀錄</p>
                    </button>
                </Link>
            </div>
        </div>
    </div>
  )
}
