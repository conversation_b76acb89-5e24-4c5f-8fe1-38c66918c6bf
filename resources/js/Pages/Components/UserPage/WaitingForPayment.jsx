import React from 'react';

const WaitingForPayment = ({ orders }) => {
    return (
        <div className="space-y-6">
            {orders.length === 0 ? (
                <div className="text-center py-8">
                    <p className="text-gray-500">暫無待付款訂單</p>
                </div>
            ) : (
                orders.map((order) => (
                    <div key={order.id} className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex justify-between items-start mb-4">
                            <div>
                                <h2 className="text-lg font-semibold main-color">
                                    訂單編號: {order.order_no}
                                </h2>
                                <p className="text-sm text-gray-500">
                                    下單時間: {new Date(order.order_date).toLocaleString()}
                                </p>
                            </div>
                            <span className="px-3 py-1 text-sm bg-yellow-100 text-yellow-800 rounded-full">
                                待付款
                            </span>
                        </div>
                        
                        <div className="space-y-4">
                            {order.product_list.map((product, index) => (
                                <div key={index} className="flex items-center gap-4 border-b pb-4">
                                    <img 
                                        src={product.image.url} 
                                        alt={product.title}
                                        className="w-20 h-20 object-cover rounded-md"
                                    />
                                    <div className="flex-1">
                                        <h3 className="font-medium">{product.title}</h3>
                                        <p className="text-sm text-gray-500">
                                            數量: {product.quantity}
                                        </p>
                                        <p className="text-sm second-color">
                                            單價: ${product.price}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                        
                        <div className="mt-4 flex justify-between items-center">
                            <div>
                                <p className="text-sm text-gray-600">
                                    總金額: <span className="font-bold second-color">${order.total_price}</span>
                                </p>
                            </div>
                            <div className="space-x-4">
                                <button 
                                    onClick={() => window.location.href = `/cart?order=${order.order_no}`}
                                    className="px-4 py-2 bg-second-color text-white rounded-lg hover:bg-opacity-90"
                                >
                                    前往付款
                                </button>
                                <button 
                                    onClick={() => {/* 取消订单逻辑 */}}
                                    className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                                >
                                    取消訂單
                                </button>
                            </div>
                        </div>
                    </div>
                ))
            )}
        </div>
    );
};

export default WaitingForPayment;
