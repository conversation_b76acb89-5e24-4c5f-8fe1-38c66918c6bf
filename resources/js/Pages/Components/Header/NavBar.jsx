import React from 'react'
import { router } from '@inertiajs/react';

export default function NavBar({ isOpen, onClose }) {
  return (
    <div 
      className={`
        fixed top-0 left-0 w-full h-screen bg-white z-50 
        transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}
    >
      <div className='md:py-4 md:px-4 py-2 px-4 w-full'>
        <div className="flex justify-between items-center mb-4">
          <div 
            className={`
              transition-all duration-500 ease-in-out transform
              ${isOpen ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4'}
            `}
          >
            <a href="/">
              <img
                src="/Icon/Logo.png"
                alt="Logo"
                width={90}
                height={"auto"}
              />
            </a>
          </div>
          <button 
            onClick={onClose}
            className={`
              transition-all duration-300 ease-in-out transform
              ${isOpen ? 'rotate-0 opacity-100' : '-rotate-90 opacity-0'}
            `}
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="#008891" className="size-8">
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="nav-menu max-w-[600px] mx-auto flex flex-col justify-center items-center gap-8 mt-16">
          <ul className="flex items-center flex-col gap-8 w-full">
            {[
              { href: '/', text: '首頁' , textEn: 'Home'},
              { href: '/user/profile', text: '會員中心' , textEn: 'Member'},
              { href: '/product', text: '產品' , textEn: 'Product'},
              { href: '/cart', text: '購物車' , textEn: 'Shopping Cart'},
            ].map((item, index) => (
              <li 
                key={item.href}
                className={`
                  w-full text-center transform
                  transition-all duration-300 ease-in-out
                  ${isOpen 
                    ? 'opacity-100 translate-y-0' 
                    : 'opacity-0 translate-y-4'
                  }
                `}
                style={{ transitionDelay: `${150 + index * 100}ms` }}
              >
                <a 
                  href={item.href} 
                  className='text-[#008891] text-lg font-bold block hover:scale-110 transition-transform duration-200'
                  // onClick={(e) => {
                  //   if (item.href === '/red-pocket-pattern') {
                  //     e.preventDefault();
                  //     router.visit('/product/red-pocket-edit');
                  //     onClose();
                  //   }
                  // }}
                >
                  <div>{item.text}</div>
                  <div>{item.textEn}</div>
                </a>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  )
}
