"use client";

import React, { useState, useEffect } from 'react';
import { Link } from "react-router-dom";
import SearchBar from '../HomePage/SearchBar';
import '../../../../css/index.css';
import NavBar from './NavBar';

const Header = () => {
    const [isSearchOpen, setIsSearchOpen] = useState(false);
    const [isNavOpen, setIsNavOpen] = useState(false);

    const toggleSearch = () => {
        setIsSearchOpen(prev => !prev);
    };

    const toggleNav = () => {
        if (!isNavOpen) {
            setIsSearchOpen(false);
        }
        setIsNavOpen(prev => !prev);
        document.body.style.overflow = !isNavOpen ? 'hidden' : '';
    };

    useEffect(() => {
        return () => {
            document.body.style.overflow = '';
        };
    }, []);

    return (
        <>
            <NavBar isOpen={isNavOpen} onClose={() => toggleNav()} />
            <header className='md:py-4 md:px-4 py-2 px-0 bg-white'>
                <div className="flex justify-between items-center md:mb-2 mb-0 px-6">
                    <div>
                        <a href="/">
                            <img
                                className='md:block hidden'
                                src="/Icon/Logo.png"
                                alt="Logo"
                                width={150}
                                height={"auto"}
                            />
                            <img
                                className='md:hidden block'
                                src="/Icon/Logo.png"
                                alt="Logo"
                                width={90}
                                height={"auto"}
                            />
                        </a>
                    </div>
                    <div className='items-center space-x-4 hidden lg:flex'>
                        <a href="/cart">
                            <img
                                src="/Icon/Cart.png"
                                alt="Cart"
                                width={30}
                                height={"auto"}
                            />
                        </a>
                        <a href="/user/login">
                            <img
                                src="/Icon/User.png"
                                className='cursor-pointer hidden lg:block'
                                alt="User"
                                width={30}
                                height={"auto"}
                            />
                        </a>
                        {/* <img
                            src="/Icon/Search.png"
                            className='cursor-pointer'
                            alt="Search_nav"
                            width={30}
                            height={"auto"}
                            onClick={toggleSearch}
                            data-search-trigger
                        /> */}
                       
                    </div>
                    <div className='flex items-center space-x-4 lg:hidden'>
                        <img
                            src="/Icon/Hamburger.png"
                            className='cursor-pointer lg:hidden md:w-8 w-6'
                            alt="Hamburger"
                            width={30}
                            height={"auto"}
                            onClick={toggleNav}
                        />
                    </div>
                </div>
                <SearchBar isSearchOpen={isSearchOpen} setIsSearchOpen={setIsSearchOpen} />
            </header>
        </>
    );
};

export default Header;
