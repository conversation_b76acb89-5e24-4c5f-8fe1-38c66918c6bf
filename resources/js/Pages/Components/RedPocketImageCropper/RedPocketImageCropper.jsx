import React, { useEffect, useRef, useState } from 'react';
import * as fabric from 'fabric';

function RedPocketImageCropper({ imageUrl, backgroundImage, onCropComplete, onImageUpload, containerRef }) {
    const canvasRef = useRef(null);
    const fabricCanvasRef = useRef(null);
    const fileInputRef = useRef(null);
    const [hasUploadedImage, setHasUploadedImage] = useState(false);
    const [isDragging, setIsDragging] = useState(false);
    const [canvasDimensions, setCanvasDimensions] = useState({
        width: 380,
        height: 194
    });

    // 监听容器尺寸变化
    useEffect(() => {
        if (!containerRef?.current) return;

        const observer = new ResizeObserver(entries => {
            for (let entry of entries) {
                const { width, height } = entry.contentRect;
                const scale = width / 672; // 704px 是设计稿宽度
                
                setCanvasDimensions({
                    width: Math.floor(380 * scale),
                    height: Math.floor(194 * scale)
                });

                // 如果画布已存在，更新其尺寸
                if (fabricCanvasRef.current) {
                    fabricCanvasRef.current.setDimensions({
                        width: Math.floor(380 * scale),
                        height: Math.floor(194 * scale)
                    });
                    fabricCanvasRef.current.renderAll();
                }
            }
        });

        observer.observe(containerRef.current);
        return () => observer.disconnect();
    }, [containerRef]);

    const CANVAS_WIDTH = canvasDimensions.width;
    const CANVAS_HEIGHT = canvasDimensions.height;

    const getCropData = () => {
        if (!fabricCanvasRef.current) return null;

        const canvas = fabricCanvasRef.current;
        const objects = canvas.getObjects();
        const uploadedImage = objects.find(obj => obj instanceof fabric.Image && obj.hasControls);

        if (!uploadedImage) return null;

        return {
            canvas: canvas,
            image: uploadedImage,
            width: CANVAS_WIDTH,
            height: CANVAS_HEIGHT
        };
    };

    const handleCanvasClick = (e) => {
        const canvas = fabricCanvasRef.current;
        if (!canvas) return;

        const pointer = canvas.getPointer(e.e);
        const objects = canvas.getObjects();
        const clickedObject = canvas.findTarget(e.e);

        if (clickedObject && clickedObject.uploadedImage) {
            return;
        }

        fileInputRef.current?.click();
    };

    const addUploadedImage = (imgUrl) => {
        if (!fabricCanvasRef.current) return;
        
        console.log('开始加载上传的图片:', imgUrl);
        const img = new Image();
        img.onload = () => {
            console.log('上传图片加载成功，原始尺寸:', img.width, 'x', img.height);
            const fabricImg = new fabric.Image(img, {
                // 保持原始质量
                objectCaching: false,
                imageSmoothingEnabled: false
            });
            
            // 计算合适的缩放比例，但保持原始分辨率
            const scale = Math.min(
                (CANVAS_WIDTH * 0.8) / img.width,
                (CANVAS_HEIGHT * 0.8) / img.height
            );

            // 计算居中位置
            const scaledWidth = img.width * scale;
            const scaledHeight = img.height * scale;
            const left = (CANVAS_WIDTH - scaledWidth) / 2;
            const top = (CANVAS_HEIGHT - scaledHeight) / 2;

            fabricImg.set({
                left: left,
                top: top,
                scaleX: scale,
                scaleY: scale,
                hasControls: true,
                hasBorders: true,
                uploadedImage: true,
                originX: 'left',
                originY: 'top'
            });
            
            // 只移除之前上传的图片，保留背景图片
            const objects = fabricCanvasRef.current.getObjects();
            objects.forEach(obj => {
                if (obj instanceof fabric.Image && obj.uploadedImage) {
                    fabricCanvasRef.current.remove(obj);
                }
            });

            fabricCanvasRef.current.add(fabricImg);
            fabricCanvasRef.current.setActiveObject(fabricImg);
            fabricCanvasRef.current.renderAll();
            setHasUploadedImage(true);

            // 触发裁剪完成回调
            if (onCropComplete) {
                onCropComplete({
                    canvas: fabricCanvasRef.current,
                    image: fabricImg,
                    width: CANVAS_WIDTH,
                    height: CANVAS_HEIGHT
                });
            }

            console.log('上传图片已添加到画布');
        };
        img.onerror = (err) => {
            console.error('上传图片加载失败:', err);
            setHasUploadedImage(false);
        };
        img.src = imgUrl;
    };

    // 修改重置函数
    const resetCanvas = () => {
        if (!fabricCanvasRef.current) return;

        // 移除所有上传的图片
        const objects = fabricCanvasRef.current.getObjects();
        objects.forEach(obj => {
            if (obj instanceof fabric.Image && obj.uploadedImage) {
                fabricCanvasRef.current.remove(obj);
            }
        });

        // 重置状态
        setHasUploadedImage(false);

        // 清除选中状态
        fabricCanvasRef.current.discardActiveObject();

        // 重新渲染画布
        fabricCanvasRef.current.renderAll();

        // 通知父组件
        if (onCropComplete) {
            onCropComplete(null);
        }

        // 清除文件输入
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    // 将拖拽处理函数移到这里
    const handleDragEnter = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
    };

    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);

        const files = e.dataTransfer.files;
        if (files && files[0]) {
            const file = files[0];
            // 检查文件类型
            if (!file.type.startsWith('image/')) {
                alert('請上傳圖片文件');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = (event) => {
                addUploadedImage(event.target.result);
            };
            reader.readAsDataURL(file);
        }
    };

    // 修改 imageUrl 监听
    useEffect(() => {
        if (!imageUrl && fabricCanvasRef.current) {
            resetCanvas();
        } else if (imageUrl && fabricCanvasRef.current) {
            addUploadedImage(imageUrl);
        }
    }, [imageUrl]);

    useEffect(() => {
        const canvas = new fabric.Canvas(canvasRef.current, {
            width: CANVAS_WIDTH,
            height: CANVAS_HEIGHT,
            selection: false,
            backgroundColor: '#f0f0f000',
            preserveObjectStacking: true,
            // 增加画布质量到8K
            devicePixelRatio: 8, // 增加到8倍
        });
        
        fabricCanvasRef.current = canvas;

        // 保存对 DOM 元素的引用
        const canvasElement = canvasRef.current;

        canvas.on('mouse:down', handleCanvasClick);

        // 添加背景图片
        const loadBackgroundImage = () => {
            return new Promise((resolve) => {
                if (backgroundImage) {
                    console.log('开始加载背景图片:', backgroundImage);
                    const img = new Image();
                    
                    // 添加跨域支持
                    img.crossOrigin = "anonymous";
                    
                    img.onload = () => {
                        console.log('背景图片加载成功，尺寸:', img.width, 'x', img.height);
                        const fabricImg = new fabric.Image(img);
                        
                        // 计算缩放比例
                        const scaleX = CANVAS_WIDTH / img.width * 2;
                        const scaleY = CANVAS_HEIGHT / img.height * 1.5;
                        
                        // 计算居中位置
                        const scaledWidth = img.width * scaleX;
                        const scaledHeight = img.height * scaleY;
                        const left = (CANVAS_WIDTH - scaledWidth) / 2;
                        const top = (CANVAS_HEIGHT - scaledHeight) / 2;

                        fabricImg.set({
                            left: left,
                            top: top,
                            scaleX: scaleX,
                            scaleY: scaleY,
                            selectable: false,
                            evented: false,
                            originX: 'left',
                            originY: 'top'
                        });
                        canvas.add(fabricImg);
                        canvas.renderAll();
                        console.log('背景图片已添加到画布');
                        resolve();
                    };
                    
                    img.onerror = (err) => {
                        console.error('背景图片加载失败:', err);
                        console.error('失败的图片URL:', backgroundImage);
                        // 不再尝试替代路径，直接解析
                        resolve();
                    };
                    
                    // 添加时间戳防止缓存
                    const timestamp = new Date().getTime();
                    img.src = `${backgroundImage}?t=${timestamp}`;
                } else {
                    console.warn('未提供背景图片径');
                    resolve();
                }
            });
        };

        // 初始化画布
        const initCanvas = async () => {
            await loadBackgroundImage();
            if (imageUrl) {
                addUploadedImage(imageUrl);
            }
        };

        initCanvas();

        // 监听对象修改事件
        canvas.on('object:modified', () => {
            const cropData = getCropData();
            if (cropData && onCropComplete) {
                onCropComplete(cropData);
            }
        });

        // 监听对象移动事件
        canvas.on('object:moving', () => {
            const cropData = getCropData();
            if (cropData && onCropComplete) {
                onCropComplete(cropData);
            }
        });

        // 监听对象缩放事件
        canvas.on('object:scaling', () => {
            const cropData = getCropData();
            if (cropData && onCropComplete) {
                onCropComplete(cropData);
            }
        });

        return () => {
            // 使用保存的引用进行清理
            if (canvasElement) {
                canvasElement.removeEventListener('drop', handleDrop);
                canvasElement.removeEventListener('dragover', handleDragOver);
            }
            if (canvas) {
                canvas.off('mouse:down', handleCanvasClick);
                canvas.dispose();
            }
            fabricCanvasRef.current = null;
        };
    }, [imageUrl, backgroundImage]);

    return (
        <div 
            className={`
                relative 
                w-full 
                aspect-[3/4] 
                overflow-hidden 
                ${
                    isDragging 
                    ? 'ring-4 ring-second-color ring-offset-2' // 使用 ring 替代 outline
                    : 'md:border-2 border-[1px] border-dashed border-gray-900'
                }
                transition-all 
                duration-300
            `}
            style={{ 
                position: 'absolute', 
                width: CANVAS_WIDTH, 
                height: CANVAS_HEIGHT,
                top: '52.5%',
                left: '50%',
                transform: 'translate(-50%, -50%)'
            }}
            onDragEnter={() => setIsDragging(true)}
            onDragLeave={() => setIsDragging(false)}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
        >
            <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                style={{ display: 'none' }}
                onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = (event) => {
                            addUploadedImage(event.target.result);
                        };
                        reader.readAsDataURL(file);
                    }
                    e.target.value = '';
                }}
            />
            <canvas ref={canvasRef} />
            
            {/* 可点击的覆盖层 */}
            {!hasUploadedImage && (
                <div 
                    className="absolute inset-0 cursor-pointer z-10"
                    onClick={() => fileInputRef.current?.click()}
                />
            )}
            
            {/* 提示文字区域 */}
            <div 
                className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300
                    ${hasUploadedImage ? 'opacity-0' : 'opacity-100'}
                    ${isDragging ? 'bg-blue-100 bg-opacity-50' : ''}`}
                style={{ pointerEvents: 'none' }}
            >
                <div className="text-center">
                    <p className="text-gray-600">
                        {isDragging ? '放開以上傳圖片' : '點擊或拖放圖片到此處'}
                    </p>
                </div>
            </div>
        </div>
    );
}

export default RedPocketImageCropper;
