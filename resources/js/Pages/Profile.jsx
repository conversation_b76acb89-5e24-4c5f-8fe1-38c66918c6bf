import React from 'react'
import { Head } from '@inertiajs/react';
import Header from '@/Pages/Components/Header/Header';
import Footer from '@/Pages/Components/Footer/Footer';
import UserAvatar from '@/Pages/Components/UserPage/UserAvatar';
import HomeBanner from '@/Pages/Components/HomePage/HomeBanner';
import UserOrder from '@/Pages/Components/UserPage/UserOrder';
import ProductRecommendHeader from '@/Pages/Components/Product/ProductRecommendHeader';
import ProductList from '@/Pages/Components/Product/ProductList';
import ButtonGroup from '@/Pages/Components/FixedButtonGroup/ButtonGroup';

export default function Profile({ auth }) {

  return (
    <div>
      <Head title={import.meta.env.VITE_APP_NAME + " - " + "用戶資料"} />
      <div className='layout-container bg-[#F2F3F3]'>
        <ButtonGroup />
        <Header />
        <UserAvatar user={auth.user} />
        <div className='rounded-2xl md:m-10 m-6 overflow-hidden'>
          <HomeBanner />
        </div>
        <UserOrder />
        <div className='md:m-10 m-6 relative'>
          <ProductRecommendHeader />
          <div className='mt-8'>
            <ProductList/>
          </div>
        </div>
        <Footer />
      </div>
    </div>
  )
}
