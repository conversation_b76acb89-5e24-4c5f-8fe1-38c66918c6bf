import React from 'react';
import { Head } from '@inertiajs/react';
import Header from '@/Pages/Components/Header/Header';
import Footer from '@/Pages/Components/Footer/Footer';
import WaitingForPayment from '@/Pages/Components/UserPage/WaitingForPayment';
import WaitingForDelivery from '@/Pages/Components/UserPage/WaitingForDelivery';
import PaymentHistory from '@/Pages/Components/UserPage/PaymentHistory';
import { Link } from '@inertiajs/react';
const UserOrderInfo = ({ orders, auth, status, title }) => {
    return (
        <>
            <Head title={title} />
            <div className="layout-container">
                <Header auth={auth} />
                <div className="container mx-auto py-8">
                    <div className='flex justify-between items-center'>
                        <h1 className="text-2xl font-bold mb-6 main-color">{title}</h1>
                        <Link href="/user/profile">
                            <div className="flex justify-end">
                                <button className="mb-4 p-2 px-4 button text-white rounded-full bg-second-color cursor-pointer">返回</button>
                            </div>
                        </Link>
                    </div>
                    
                    {/* 根据状态显示不同组件 */}
                    {status === 'waiting-for-payment' && (
                        <WaitingForPayment orders={orders} />
                    )}
                    
                    {status === 'waiting-for-delivery' && (
                        <WaitingForDelivery orders={orders} />
                    )}

                    {status === 'payment-history' && (
                        <PaymentHistory orders={orders} />
                    )}
                </div>
                <Footer />
            </div>
        </>
    );
};

export default UserOrderInfo;
