import React, { useEffect } from 'react';

const Vcard = () => {
    useEffect(() => {
        // 检测是否为移动设备
        const isMobileDevice = /Mobi|Android/i.test(navigator.userAgent);
        if (isMobileDevice) {
            // 重定向到 VCF 文件
            window.location.href = '/Icon/Vcard/Sample.vcf';
        }
    }, []);

    return (
        <div>
            <div className="flex flex-col items-center justify-center h-screen">
                <h1 className="text-4xl font-bold">Vcard</h1>
                <a href="/Icon/Vcard/Sample.vcf" type="text/vcard">Add to Contact</a>
            </div>
        </div>
    );
};

export default Vcard;
