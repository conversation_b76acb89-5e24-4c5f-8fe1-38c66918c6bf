import React from 'react';
import { Head } from '@inertiajs/react';
import Header from '@/Pages/Components/Header/Header';
import Footer from '@/Pages/Components/Footer/Footer';
import ButtonGroup from '@/Pages/Components/FixedButtonGroup/ButtonGroup';
import ProductTabs from '@/Pages/Components/Product/ProductTabs';
import ProductDetail from '@/Pages/Components/Product/ProductDetail';
import '../../css/index.css';

const Product = (props) => {
    const isDetail = props.id ? true : false;
    return (  
        <>
            <Head title={import.meta.env.VITE_APP_NAME + " - " + "產品詳情"} />
            <div className='layout-container'>
                <div className="">
                    <ButtonGroup />
                    <Header />
                    {isDetail && <ProductDetail id={props.id} />}
                    {!isDetail && <div className="mx-auto px-4 md:py-8 py-0">
                        <ProductTabs />
                    </div>}
                </div>
              <Footer />
            </div>
        </>
    );
};

export default Product;
