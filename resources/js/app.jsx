import React from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter as Router } from 'react-router-dom';
import { createInertiaApp } from '@inertiajs/react';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import Header from '@/Pages/Components/Header/Header';
import '../css/app.css';
import { CartProvider } from '@/Pages/Contexts/CartContext';

createInertiaApp({
    resolve: (name) => resolvePageComponent(`./Pages/${name}.jsx`, import.meta.glob('./Pages/**/*.jsx')),
    setup({ el, App, props }) {
        const root = createRoot(el);
        
        root.render(
            // <React.StrictMode>
                <Router>
                    <CartProvider user={props?.auth?.user || null}>
                        <App {...props} />
                    </CartProvider>
                </Router>
            // </React.StrictMode>
        );
    },
});
