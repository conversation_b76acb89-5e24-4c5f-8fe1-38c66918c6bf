<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Cart
 */
class Cart extends Model
{
	use SoftDeletes;

	protected $fillable = [
		'uuid',
		'user_id',
		'client_project_list',
		'cart_meta',
	];

	protected $casts = [
		'client_project_list' => 'json',
		'cart_meta' => 'json',
	];

	public function user()
	{
		return $this->belongsTo(User::class);
	}

	protected $attributes = [
		'client_project_list' => '[]',
		'cart_meta' => '[]'
	];

	public function clientProjects()
	{
		return ClientProject::whereIn('id', collect($this->cart_meta)->pluck('client_project_id'));
	}

	public function products()
	{
		return Product::whereIn('id', collect($this->cart_meta)->pluck('product_id'));
	}

	// 获取购物车项目的完整信息
	public function getCartItemsAttribute()
	{
		$cartMeta = collect($this->cart_meta);
		$clientProjects = $this->clientProjects()->with('product')->get();

		return $cartMeta->map(function ($item) use ($clientProjects) {
			$clientProject = $clientProjects->firstWhere('id', $item['client_project_id']);
			
			return array_merge($item, [
				'product_name' => $clientProject->product->title ?? null,
				'product_image' => $clientProject->product->images[0]['url'] ?? null,
				'price' => $clientProject->product->price ?? 0,
				'specs' => $clientProject->product->specs ?? null,
			]);
		});
	}

	// public function clientProject()
	// {
	// 	return $this->belongsTo(ClientProject::class);
	// }
}