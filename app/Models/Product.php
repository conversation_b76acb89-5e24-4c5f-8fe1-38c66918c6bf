<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Slowlyo\OwlAdmin\Models\BaseModel as Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * Products
 */
class Product extends Model
{
	protected $table = 'products';

	protected $fillable = [
		'title',
		'description',
		'price',
		'images',
		'specs',
		'meta',
		'limit_unit',
		'active',
	];

	protected $casts = [
		'active' => 'boolean',
		'specs' => 'array',
		'images' => 'array',
		'meta' => 'json',
		'price' => 'decimal:2'
	];

	protected function images(): Attribute
	{
		return Attribute::make(
			get: fn ($value) => is_string($value) ? json_decode($value, true) : $value,
			set: fn ($value) => is_array($value) ? json_encode($value) : $value
		);
	}

	public function orders()
	{
		return $this->hasMany(Order::class, 'product_id', 'id');
	}

	// client project
	public function clientProjects()
	{
		return $this->hasMany(ClientProject::class, 'product_id', 'id');
	}

	public function getNameAttribute()
	{
		return $this->attributes['name'];
	}

	public function getTitleAttribute()
	{
		return $this->attributes['title'];
	}

	use SoftDeletes;
}