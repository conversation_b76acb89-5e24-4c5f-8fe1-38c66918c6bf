<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Slowlyo\OwlAdmin\Models\BaseModel as Model;

/**
 * orders
 */
class Order extends Model
{
	protected $table = 'orders';

	protected $fillable = [
		'user_id',
		'product_id',
		'total_price',
		'address',
		'phone',
		'email',
		'payment_method',
		'status',
		'order_date',
		'delivery_date',
		'transition_id',
		'product_list',
		'product_meta',
		'note',
		'order_no',
		'discount',
		'coupon_code',
		'shipping',
		'paid_date',
		'recipient',
		'delivery_method',
		'payment_meta',
	];

	protected $dates = [
		'order_date',
		'delivery_date',
	];

	protected $casts = [
		'order_date' => 'datetime',
		'delivery_date' => 'datetime',
		'product_list' => 'array',
		'product_meta' => 'array',
		'product_id' => 'array',
	];

	public const ORDER_STATUS = [
		// wait for payment
        "1" => "Pending",
        // Paid
        "2" => "Paid",
        // delivered
        "3" => "Delivered",
        // completed
        "4" => "Completed",
        // cancelled
        "5" => "Cancelled",
        // deleted
        "9" => "Deleted",
    ];


	public const PAYMENT_METHOD = [
		"1" => "Stripe",
		"2" => "Credit Card",
		"3" => "Bank Transfer",
		"4" => "Cash",
	];

	public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

	public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

	public function productList()
	{
		// product_list is a json array, each item is a product id
		return $this->hasMany(Product::class, 'product_list', 'product_id');
	}


	public function invoice()
	{
		return $this->belongsTo(ClientInvoice::class, 'invoice_id', 'id');
	}

	use SoftDeletes;
}
