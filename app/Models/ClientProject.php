<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ClientProject extends Model
{
	protected $fillable = [
		'product_template_id',
		'product_id',
		'user_id',
		'local_uuid',
		'saved',
		'name',
		'description',
		'url',
		'status',
		'project_data',
		// 'delivery_info',
		// 'quantity',
		// 'meta'
	];

	protected $with = ['user', 'product'];

	protected $casts = [
		'status' => 'integer',
		'project_data' => 'json',
	];

	public const CLIENT_PROJECT_STATUS = [
		"1" => "Draft",
		"2" => "Published",
		"3" => "Archived",
	];

	public function user()
	{
		return $this->belongsTo(User::class);
	}

	public function product()
	{
		return $this->belongsTo(Product::class, 'product_id', 'id');
	}
}
