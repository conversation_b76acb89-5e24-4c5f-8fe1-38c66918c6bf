<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientInvoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'order_id',
        'order_no',
        'local_uuid',
        'uuid',
        'delivery_detail',
        'cart',
        'coupon_code',
        'currency',
        'subtotal',
        'shipping',
        'discount',
        'total',
        'payment_method',
        'status',
        'paid_at',
        'delivery_method',
		'payment_meta',
    ];

    protected $casts = [
        'delivery_detail' => 'array',
        'cart' => 'array',

    ];

    function user() {
        return $this->belongsTo(User::class);
    }

    function hasCoupon() {
        return $this->coupon_code !== null;
    }
    
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }

    public const INVOICE_STATUS = [
        '1' => 'Pending',
        '2' => 'Paid',
        '3' => 'Cancelled',
        '4' => 'Refunded'
    ];

    public const PAYMENT_METHOD = [
		"1" => "Stripe",
		"2" => "Credit Card",
		"3" => "Bank Transfer",
		"4" => "Cash",
	];

    // function applyCoupon( $coupon ) {
    //     if ( Coupon::where('code', $coupon)->doesntExist() ) {
    //         return false;
    //     }
    //     $this->coupon_code = $coupon->code;
    //     $this->save();
    // }

    // function isValidCoupon() {
    //     if ( $this->hasCoupon() ) {
    //         $coupon = Coupon::where('code', $this->coupon_code)->firstOrFail();
    //         return $coupon->isValid();
    //     }
    // }
}
