<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\Support\Facades\Storage;
use App\Models\DeliveryAddress;

class AuthController extends Controller
{

    // public function socialmediaCallback(Request $request)
    // {
    //     dd('auth here');
    // }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'username' => ['required'],
            'password' => ['required'],
        ]);
        // if (Auth::attempt($credentials)) {
        // check user is exists 1. email 2. name 3.password
        $check_user = User::where('email', $request->username)->orWhere('name', $request->username)->first();
        if(!$check_user) {
            return response()->json(['status' => 400, 'message' => '用戶不存在']);
        }
        $check_password = Hash::check($request->password, $check_user->password);
        if(!$check_password) {
            return response()->json(['status' => 400, 'message' => '密碼錯誤']);
        }
        // login success
        Auth::login($check_user);
        $request->session()->regenerate();
        return response()->json(['status' => 200, 'message' => '登入成功']);
        // return redirect('/user/profile');
        // }
        


        return back()->withErrors([
            'message' => '登入資訊不正確。',
        ]);
    }

    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', Rules\Password::defaults()],
        ]);

        // Check if the user already exists
        $exists_user = User::where('email', $request->email)->orWhere('name', $request->name)->first();
        if($exists_user) {
            return response()->json(['status' => 400, 'message' => '用戶已存在']);
        }
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);
        if($user) {
            return response()->json(['status' => 200, 'message' => '註冊成功']);
        } else {
            return response()->json(['status' => 400, 'message' => '註冊失敗']);
        }
    }

    public function logout()
    {
        Auth::logout();
        session()->invalidate();
        session()->regenerateToken();
        
        return redirect('/user/login');
    }

    public function updateAvatar(Request $request)
    {
        $request->validate([
            'avatar' => 'required|image|mimes:svg,jpeg,png,jpg,gif|max:2048'
        ]);

        try {
            if ($request->hasFile('avatar')) {
                // 删除旧头像
                $user = Auth::user();
                if ($user->avatar) {
                    $oldPath = str_replace('/storage/', '', $user->avatar);
                    Storage::disk('public')->delete($oldPath);
                }

                // 保存新头像
                $path = $request->file('avatar')->store('avatars', 'public');
                
                // 更新用户头像
                $user->avatar = '/storage/' . $path;
                $user->save();

                // 返回 Inertia 响应
                return redirect()->back()->with([
                    'user' => $user,
                ]);
            }

            return redirect()->back()->withErrors([
                'avatar' => '未找到上傳的文件'
            ]);
        } catch (\Exception $e) {
            \Log::error('Avatar upload failed: ' . $e->getMessage());
            return redirect()->back()->withErrors([
                'avatar' => '頭像更新失敗'
            ]);
        }
    }

    public function updateAddress(Request $request)
    {
        $request->validate([
            'address' => 'required|json',
        ]);

        try {
            $user = Auth::user();
            $addresses = json_decode($request->address, true);

            // 确保地址数组不为空
            if (empty($addresses)) {
                return redirect()->back()->withErrors(['message' => '至少需要一個地址']);
            }

            // 处理默认地址逻辑
            $hasDefault = false;
            foreach ($addresses as $address) {
                if (isset($address['is_default']) && $address['is_default']) {
                    $hasDefault = true;
                    break;
                }
            }

            // 如果没有默认地址，将第一个设为默认
            if (!$hasDefault) {
                $addresses[0]['is_default'] = true;
            }

            // 如果只有一个地址，强制设为默认
            if (count($addresses) === 1) {
                $addresses[0]['is_default'] = true;
            }

            // 确保只有一个默认地址
            $defaultFound = false;
            foreach ($addresses as &$address) {
                if (isset($address['is_default']) && $address['is_default']) {
                    if ($defaultFound) {
                        $address['is_default'] = false;
                    }
                    $defaultFound = true;
                }
            }

            $user->address = $addresses;
            $user->save();

            return redirect()->back()->with('success', '地址更新成功');
        } catch (\Exception $e) {
            \Log::error('Address update failed: ' . $e->getMessage());
            return redirect()->back()->withErrors(['message' => '地址更新失敗']);
        }
    }

    public function updateProfile(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . Auth::id(),
            'phone' => 'required|string|max:255',
        ]);

        try {
            $user = Auth::user();
            $user->update($request->only(['name', 'email', 'phone']));
            
            return redirect()->back()->with('success', '基本資料更新成功');
        } catch (\Exception $e) {
            return redirect()->back()->withErrors(['message' => '更新失敗']);
        }
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'old_password' => 'required',
            'new_password' => 'required|min:8|different:old_password',
            'confirm_password' => 'required|same:new_password',
        ]);

        try {
            $user = Auth::user();
            
            if (!Hash::check($request->old_password, $user->password)) {
                return redirect()->back()->withErrors(['old_password' => '當前密碼不正確']);
            }

            $user->password = Hash::make($request->new_password);
            $user->save();
            
            return redirect()->back()->with('success', '密碼更新成功');
        } catch (\Exception $e) {
            return redirect()->back()->withErrors(['message' => '密碼更新失敗']);
        }
    }
} 