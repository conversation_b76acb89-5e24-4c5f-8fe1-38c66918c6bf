<?php

namespace App\Http\Controllers;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\ClientProject;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ClientProjectController extends Controller
{
    public function store(Request $request)
    {
        if(!Auth::check()) {
            return redirect('/user/login');
        }
        try {
            // 保存上传的图片
            $image = $request->file('image');
            
            // 解析项目数据
            $projectData = json_decode($request->project_data, true);

            // 如果有 uuid，先查找现有项目
            $uuid = $request->uuid ?? Str::uuid();
            
            // 创建或更新项目记录
            $project = ClientProject::updateOrCreate(
                [
                    'local_uuid' => $uuid,
                    'user_id' => auth()->id(),
                    'product_id' => $request->product_id,
                ],
                [
                    'product_template_id' => $request->pattern,
                    'name' => $request->name,
                    'status' => 1,
                    'project_data' => $projectData
                ]
            );

            // 保存图片
            $imagePath = $image->store('client_projects/originals/'.$project->id, 'public');
            $projectData['image']['stored_url'] = Storage::url($imagePath);
            
            // 保存 PDF
            if ($request->hasFile('pdf')) {
                $pdfPath = $request->file('pdf')->store('client_projects/originals/'.$project->id, 'public');
                $projectData['pdf_url'] = Storage::url($pdfPath);
            }
            
            // 更新项目数据
            $project->update(['project_data' => $projectData]);
            
            return response()->json([
                'success' => true,
                'message' => '保存成功',
                'data' => $project
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '保存失敗: ' . $e->getMessage()
            ], 500);
        }
    }
} 