<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class UserController extends Controller
{
    public function getAddresses(Request $request)
    {
        $user = $request->user();
        
        // 从 JSON 字符串解析地址
        $addresses = $user->address;
        if (empty($addresses)) {
            return response()->json([
                'addresses' => [
                    'name' => 'default',
                    'recipient' => $user->name,
                    'phone' => '',
                    'address' => '',
                    'notes' => '',
                    'is_default' => true,
                ]
            ]);
        }
        else {
            return response()->json([
                'success' => true,
                'addresses' => $addresses
            ]);
        }
        
    }

    public function updateAddresses(Request $request)
    {
        $request->validate([
            'addresses' => 'required|array',
            'addresses.*.recipient_name' => 'required|string',
            'addresses.*.phone' => 'required|string',
            'addresses.*.address' => 'required|string',
            'addresses.*.notes' => 'nullable|string',
            'addresses.*.is_default' => 'boolean'
        ]);

        try {
            $user = $request->user();
            $addresses = $request->addresses;

            // 确保只有一个默认地址
            $defaultFound = false;
            foreach ($addresses as &$address) {
                if ($address['is_default']) {
                    if ($defaultFound) {
                        $address['is_default'] = false;
                    }
                    $defaultFound = true;
                }
            }

            // 如果没有默认地址，将第一个设为默认
            if (!$defaultFound && !empty($addresses)) {
                $addresses[0]['is_default'] = true;
            }

            // 保存为 JSON 字符串
            $user->address = json_encode($addresses);
            $user->save();

            // 同时更新 delivery_info
            $defaultAddress = collect($addresses)->firstWhere('is_default', true);
            if ($defaultAddress) {
                $user->delivery_info = $defaultAddress;
                $user->save();
            }

            return response()->json([
                'success' => true,
                'message' => '地址更新成功',
                'addresses' => $addresses
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '地址更新失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 