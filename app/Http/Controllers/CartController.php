<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Cart;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CartController extends Controller
{
    public function add(Request $request)
    {
        try {
            // 验证请求数据
            $request->validate([
                'clientProject' => 'required|array',
                'clientProject.id' => 'required',
                'clientProject.product_id' => 'required',
                'clientProject.local_uuid' => 'required',
            ]);

            // 获取或创建购物车
            $cart = Cart::updateOrCreate(
                [
                    'user_id' => auth()->id(),
                ],
                [
                    'uuid' => Str::uuid(),
                ]
            );

            // 准备新的项目数据
            $newItem = [
                'client_project_id' => $request->clientProject['id'],
                'quantity' => 1,
                'product_id' => $request->clientProject['product_id'],
                'local_uuid' => $request->clientProject['local_uuid'],
            ];

            // 获取现有的购物车数据
            $cartMeta = $cart->cart_meta ?? [];
            $projectList = $cart->client_project_list ?? [];

            // 添加新项目
            $cartMeta[] = $newItem;
            $projectList[] = [
                'client_project_id' => $request->clientProject['id']
            ];

            // 更新购物车
            $cart->update([
                'cart_meta' => $cartMeta,
                'client_project_list' => $projectList
            ]);

            return response()->json([
                'success' => true,
                'message' => '成功加入購物車',
                'data' => $cart
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '加入購物車失敗',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function list()
    {
        try {
            $cart = Cart::where('user_id', auth()->id())->first();
            
            if (!$cart) {
                return response()->json([
                    'success' => true,
                    'data' => null
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'uuid' => $cart->uuid,
                    'cart_meta' => $cart->cart_items  // 使用新的访问器
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '獲取購物車失敗',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function update(Request $request)
    {
        try {
            $cart = Cart::where('user_id', auth()->id())->first();
            
            if (!$cart) {
                return response()->json([
                    'success' => false,
                    'message' => '購物車不存在'
                ], 404);
            }

            $cartMeta = collect($cart->cart_meta)->map(function ($item) use ($request) {
                if ($item['client_project_id'] == $request->client_project_id) {
                    $item['quantity'] = $request->quantity;
                }
                return $item;
            })->toArray();

            $cart->update(['cart_meta' => $cartMeta]);

            return response()->json([
                'success' => true,
                'message' => '數量已更新'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '更新失敗'
            ], 500);
        }
    }

    public function destroy($clientProjectId)
    {
        try {
            $cart = Cart::where('user_id', auth()->id())->first();
            
            if (!$cart) {
                return response()->json([
                    'success' => false,
                    'message' => '購物車不存在'
                ], 404);
            }

            $cartMeta = collect($cart->cart_meta)
                ->filter(function ($item) use ($clientProjectId) {
                    return $item['client_project_id'] != $clientProjectId;
                })->values()->toArray();

            $projectList = collect($cart->client_project_list)
                ->filter(function ($item) use ($clientProjectId) {
                    return $item['client_project_id'] != $clientProjectId;
                })->values()->toArray();

            $cart->update([
                'cart_meta' => $cartMeta,
                'client_project_list' => $projectList
            ]);

            return response()->json([
                'success' => true,
                'message' => '商品已移除'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '刪除失敗'
            ], 500);
        }
    }

    public function getCount()
    {
        try {
            $cart = Cart::where('user_id', auth()->id())->first();
            
            if (!$cart || empty($cart->cart_meta)) {
                return response()->json(['count' => 0]);
            }

            // 计算所有商品的数量总和
            $count = collect($cart->cart_meta)->sum('quantity');
            
            return response()->json(['count' => $count]);
        } catch (\Exception $e) {
            return response()->json(['count' => 0]);
        }
    }
} 