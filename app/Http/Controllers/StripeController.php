<?php

namespace App\Http\Controllers;

use Stripe\Stripe;
use App\Models\Order;
use Stripe\PaymentIntent;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\ClientInvoice;

class StripeController extends Controller
{
    public function __construct()
    {
        Stripe::setApiKey(env('VITE_STRIPE_SECRET_KEY'));
    }


    public function generateOrderNo()
    {
        // Generate a unique order number by date and sort number
        $date = date('Ymd');
        $TodayOrder = 'DSP' . $date ;
        $sort = Order::where('order_no', 'like', $TodayOrder . '%')->count() + 1;
        $orderNo = 'DSP' . $date . '-' . str_pad($sort, 5, '0', STR_PAD_LEFT);
        if (Order::where('order_no', $orderNo)->exists()) {
            return $this->generateOrderNo();
        }
        return $orderNo;
    }

    public function checkout(Request $request)
    {
            $amount = $request->amount;
            $orderData = $request->orderData;
            // get orderData expect price,specs,image
            // dd($orderData);
            // 获取产品列表
            $productList = array_map(function($item) {
                return [
                    'product_id' => $item['product_id'],
                    'uuid' => $item['uuid'],
                    'title' => $item['title'],
                    'price' => $item['price'],
                    'quantity' => $item['quantity'],
                    'image' => $item['image'],
                    'client_project_id' => $item['client_project_id'],
                ];
            }, $orderData['items']);
            
            // 先创建订单记录
            $order = Order::updateOrCreate(
                [
                    'order_no' => $orderData['order_no'],
                ],
                [
                    'uuid' => Str::uuid(),
                    'user_id' => auth()->id(),
                    'product_id' => $orderData['items'][0]['product_id'] ?? null,
                    'total_price' => $amount,
                    'recipient' => $orderData['delivery_info']['recipient'] ?? '',
                    'address' => $orderData['delivery_info']['address'] ?? '',
                    'phone' => $orderData['delivery_info']['phone'] ?? '',
                    'email' => auth()->user()->email,
                    'payment_method' => 1, // Credit Card
                    'status' => 1, // Pending
                    'order_date' => now(),
                    'product_list' => $productList,
                    'product_meta' => $orderData['items'],
                    'note' => $orderData['delivery_info']['note'] ?? '',
                    'shipping' => $orderData['delivery_method'] === 'delivery' ? 20 : 0,
                    'order_no' => $this->generateOrderNo(),
                    'delivery_method' => $orderData['delivery_method'],
                    // 'meta' => [
                    //     'items' => $orderData['items'],
                    //     'delivery_method' => $orderData['delivery_method'],
                    //     'delivery_info' => $orderData['delivery_info']
                    // ]
                ]
            );
            // 创建支付意图，只存储必要的元数据
            $paymentIntent = PaymentIntent::create([
                'amount' => round($amount * 100),
                'currency' => 'hkd',
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
                'metadata' => [
                    'order_id' => $order->id,
                    'user_id' => auth()->id()
                ],
                'description' => "Order #{$order->id} - " . count($orderData['items']) . " items"
            ]);

            // 更新订单的支付意图 ID
            $order->update([
                'transition_id' => $paymentIntent->id,
                'payment_meta' => [
                    'payment_intent' => $paymentIntent,
                ]
            ]);

            return response()->json([
                'success' => true,
                'clientSecret' => $paymentIntent->client_secret,
                'orderId' => $order->id
            ]);
    }

    public function confirm(Request $request)
    {
        try {
            $paymentIntentId = $request->payment_intent_id;
            
            // 获取支付意图
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            
            // 查找对应订单
            $order = Order::where('transition_id', $paymentIntentId)->first();
            
            if (!$order) {
                throw new \Exception('找不到對應的訂單');
            }

            if ($paymentIntent->status === 'succeeded') {
                // add transaction method
                // 创建客户发票
                $clientInvoice = ClientInvoice::create([
                    'uuid' => Str::uuid(),
                    'user_id' => auth()->id(),
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'delivery_detail' => [
                        'recipient' => $order->recipient,
                        'address' => $order->address,
                        'phone' => $order->phone,
                        'email' => $order->email,
                    ],
                    'cart' => $order->product_list,
                    'coupon_code' => $order->coupon_code,
                    'currency' => $order->currency,
                    'discount' => $order->discount,
                    'shipping' => $order->shipping,
                    'total' => $order->total_price,
                    'payment_method' => $order->payment_method,
                    'paid_at' => $order->paid_date,
                    'delivery_method' => $order->delivery_method,
                    'payment_meta' => $order->payment_meta,
                ]);

                // update order status
                $order->status = '2'; // Paid
                $order->paid_date = now();
                $order->invoice_id = $clientInvoice->id;
                $order->save();

                return response()->json([
                    'success' => true,
                    'order_id' => $order->id,
                    'invoice_uuid' => $clientInvoice->uuid,
                    'redirect_url' => "/thankyou/{$clientInvoice->uuid}"
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => '支付未完成'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
} 