<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\HtmlString;

class ProductController extends Controller
{
    public function index()
    {
        try {
            $products = Product::where('active', true)
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($product) {
                    return [
                        'id' => $product->id,
                        'title' => $product->title,
                        'description' => $product->description,
                        'price' => $product->price,
                        'images' => $product->images,
                        'specs' => $product->specs,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $products
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取产品列表失败'
            ], 500);
        }
    }

    public function show($id)
    {
        try {
            $product = Product::findOrFail($id);
            
            // 处理描述中的 HTML
            $description = $product->description;
            $description = preg_replace('/<p>(.*?)<\/p>/', "$1\n", $description); // 将 <p> 标签内容转换为新行
            $description = str_replace('&nbsp;', ' ', $description); // 替换空格
            $description = str_replace('<br />', "\n", $description); // 将 <br /> 转换为换行符
            $description = str_replace('<br>', "\n", $description); // 将 <br> 转换为换行符
            $description = trim($description); // 移除多余的空白
            
            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $product->id,
                    'title' => $product->title,
                    'description' => $description,
                    'price' => $product->price,
                    'images' => $product->images,
                    'specs' => $product->specs,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取产品数据失败'
            ], 500);
        }
    }
} 