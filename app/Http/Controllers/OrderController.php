<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    public function index()
    {
        // return view('order.index');
    }

    public function confirmDelivery(Request $request, $id)
    {
        $order = Order::find($id);
        $order->update([
            'status' => 4,
            'delivery_date' => now(),
        ]);
        return response()->json(['success' => true]);
    }
} 
