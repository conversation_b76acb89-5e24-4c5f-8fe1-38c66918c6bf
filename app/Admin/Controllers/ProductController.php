<?php

namespace App\Admin\Controllers;

use App\Services\ProductService;
use Slowlyo\OwlAdmin\Controllers\AdminController;

/**
 * Products
 *
 * @property ProductService $service
 */
class ProductController extends AdminController
{
	protected string $serviceName = ProductService::class;

	public function list()
	{
		$crud = $this->baseCRUD()
			->filterTogglable(false)
			->headerToolbar([
				$this->createButton('dialog'),
				...$this->baseHeaderToolBar()
			])
			->columns([
				amis()->TableColumn('id', 'ID')->sortable(),
				amis()->TableColumn('title', 'Title'),
				// amis()->NumberControl('limit_unit', 'LimitUnit'),
				amis()->SwitchControl('active', 'Active')->falseValue('0')->trueValue('1'),				
				amis()->TableColumn('created_at', admin_trans('admin.created_at'))->type('datetime')->sortable(),
				amis()->TableColumn('updated_at', admin_trans('admin.updated_at'))->type('datetime')->sortable(),
				$this->rowActions('dialog')
			]);

		return $this->baseList($crud);
	}

	


	public function form($isEdit = false)
	{
		return $this->baseForm()->body([
			amis()->TextControl('title', 'Title'),
			amis()->RichTextControl('description', 'Description'),
			amis()->NumberControl('price', 'Price'),
			// amis()->ComboControl()->label('Image List')->name('images','Images')->draggable(true)->multiple('1')->addable('1')->removable('1')->addBtn([
			// 	'label' => 'Add',
			// 	'icon' => 'fa fa-plus',
			// 	'level' => 'primary',
			// 	'size' => 'sm',
			// ])->items([
			// 		amis()->ImageControl('url', 'URL')
			// 			->hidden(false)
			// 			->disabledOn(true)
			// 			->listable(true)
			// 			->sortable(true)
			// 			->autoUpload(true)
			// 			->receiver(admin_url('upload/image/products'))
			// 			->accept('image/*')
			// 			->imageClassName('r w-full')
			// 			->data([
			// 				'folder' => 'products',
			// 			])
			// 			->proxy(false)
			// 			->headers([
			// 				'X-CSRF-TOKEN' => csrf_token(),
			// 			])
			// 			->responseType('json')
			// 			->submitType('multipart/form-data')
			// 			->withCredentials(true)
			// 			->joinValues(false)
			// 			->extractValue(true)
			// 			->delimiter(','),
			// 		amis()->SwitchControl('hidden', 'hidden')->falseValue('0')->trueValue('1'),
			// ]),

			amis()->ImageControl('images', 'Images')
				->draggable(true)
				->multiple(true)
				->autoUpload(true)
				->receiver(admin_url('upload/image/products'))
				->accept('image/*')
				->imageClassName('r w-full')
				->data([
					'folder' => 'products',
				])
				->proxy(false)
				->headers([
					'X-CSRF-TOKEN' => csrf_token(),
				])
				->withCredentials(true)
				->joinValues(false)
				->delimiter(','),
			amis()->SubFormControl('specs', 'Specs')->label('Specs')->form([
				'title' => 'Specs',
				'body' => [
					amis()->TextControl()->label('Size')->name('size'),
					amis()->TextControl()->label('Page')->name('page'),
					amis()->TextControl()->label('Delivery')->name('delivery'),
					amis()->TagControl()->label('Tag')->name('tag')
						->multiple(true)
						->withCredentials(true)
						->joinValues(false)
						->extractValue(true)
						->delimiter(',')
						->options([
							[
								'label' => '新品上市',
								'value' => '新品上市',
							],
							[
								'label' => '人氣商品',
								'value' => '人氣商品',
							],
							[
								'label' => '推薦商品',
								'value' => '推薦商品',
							],
						]),
					amis()->TextareaControl()->label('Product Detail')->name('detail'),
				],
			]),
			amis()->NumberControl('limit_unit', 'LimitUnit'),
			amis()->SwitchControl('active', 'Active')->falseValue('0')->trueValue('1'),
		]);
	}

	public function detail()
	{
		return $this->baseDetail()->body([
			amis()->TextControl('id', 'ID')->static(),
			amis()->TextControl('title', 'Title')->static(),
			amis()->RichTextControl('description', 'Description')->static(),
			amis()->NumberControl('price', 'Price')->static(),
			amis()->NumberControl('limit_unit', 'LimitUnit')->static(),
			amis()->SubFormControl('specs', 'Specs')->label('Specs')->form([
				'title' => 'Specs',
				'body' => [
					amis()->TextControl()->label('Size')->name('size'),
					amis()->TextControl()->label('Page')->name('page'),
					amis()->TextControl()->label('Delivery')->name('delivery'),
					amis()->TextareaControl()->label('Product Detail')->name('detail'),
				],
			]),
			// amis()->SwitchControl('active', 'Active')->falseValue('0')->trueValue('1'),
			amis()->TextControl('created_at', admin_trans('admin.created_at'))->static(),
			amis()->TextControl('updated_at', admin_trans('admin.updated_at'))->static(),
		]);
	}
}