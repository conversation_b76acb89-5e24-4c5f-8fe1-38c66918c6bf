<?php

namespace App\Admin\Controllers;

use Illuminate\Http\Request;
use Slowlyo\OwlAdmin\Controllers\AdminController;
use Illuminate\Support\Facades\Storage;

class UploadController extends AdminController
{
    public function adminUploadImage(Request $request)
    {
        try {
            if (!$request->hasFile('file')) {
                return $this->response()->fail('請選擇要上傳的文件');
            }

            $file = $request->file('file');
            $path = $file->store($request->folder, 'public');
            $url = Storage::url($path);

            // 返回 amis 上传接口所需的格式
            return response()->json([
                'status' => 0,
                'msg' => '上傳成功',
                'data' => [
                    'value' => $url,
                    'url' => $url,
                    'path' => $path,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 1,
                'msg' => '上傳失敗：' . $e->getMessage()
            ]);
        }
    }
} 