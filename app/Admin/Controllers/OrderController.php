<?php

namespace App\Admin\Controllers;

use App\Models\Order;
use App\Services\OrderService;
use Illuminate\Support\Facades\Auth;
use Slowlyo\OwlAdmin\Controllers\AdminController;

/**
 * orders
 *
 * @property OrderService $service
 */
class OrderController extends AdminController
{
	protected string $serviceName = OrderService::class;

	public function list()
	{
		$crud = $this->baseCRUD()
			->filterTogglable(false)
			->headerToolbar([
				$this->createButton('dialog'),
				...$this->baseHeaderToolBar()
			])
			->columns([
				amis()->TableColumn('id', 'ID')->sortable(),
				amis()->TableColumn('user_id', 'User ID'),
				amis()->TableColumn('total_price', 'Total Price')
					->sortable()
					->type('number')
					->precision(2)
					->prefix('$'),
				// amis()->TableColumn('product_list', '商品列表')
				// 	->type('json')
				// 	->value(function ($item) {
				// 		return array_map(function($product) {
				// 			return [
				// 				'title' => $product['title'],
				// 				'quantity' => $product['quantity'],
				// 				'price' => '$' . number_format($product['price'], 2)
				// 			];
				// 		}, $item->product_list ?? []);
				// 	})
				// 	->listItem([
				// 		amis()->Property()->label('商品名稱')->content('${title}'),
				// 		amis()->Property()->label('數量')->content('${quantity}'),
				// 		amis()->Property()->label('單價')->content('${price}')
				// 	]),
				amis()->TableColumn('address', 'Delivery Address'),
				amis()->TableColumn('phone', 'Phone')->sortable(),
				amis()->TableColumn('email', 'Email'),
				amis()->TableColumn('payment_method', 'Payment Method')
					->type('mapping')
					->map(Order::PAYMENT_METHOD),
				amis()->TableColumn('status', 'Order Status')
					->type('mapping')
					->map(Order::ORDER_STATUS),
				amis()->TableColumn('order_date', 'Order Date')
					->type('datetime')
					->format('YYYY-MM-DD HH:mm:ss')
					->sortable(),
				amis()->TableColumn('paid_date', 'Paid Date')
					->type('datetime')
					->format('YYYY-MM-DD HH:mm:ss')
					->sortable(),
				// amis()->TableColumn('delivery_date', '發貨時間')
				// 	->type('datetime')
				// 	->format('YYYY-MM-DD HH:mm:ss')
				// 	->sortable(),
				amis()->TableColumn('updated_at', 'Updated Time')
					->type('datetime')
					->format('YYYY-MM-DD HH:mm:ss')
					->sortable(),
				$this->rowActions('dialog')
			])
			->id('crud');  // 设置 CRUD 组件的 ID

		return $this->baseList($crud);
	}

	public function form($isEdit = false)
	{
		return $this->baseForm()->body([
			amis()->TextControl('user_id', 'User ID'),
			amis()->TableControl('product_list', 'Product List')
			->source('${product_list}')
			->columns([
				amis()->TableColumn('title', 'Product Name'),
				amis()->TableColumn('quantity', 'Quantity'),
				amis()->TableColumn('price', 'Price')
					->type('number')
					->precision(2)
					->prefix('$'),
				amis()->TableColumn('uuid', 'Order ID'),
			]),
			amis()->NumberControl('total_price', 'Total Price')
				->precision(2)
				->prefix('$'),
			amis()->TextControl('address', 'Delivery Address'),
			amis()->TextControl('phone', 'Phone'),
			amis()->TextControl('email', 'Email'),
			amis()->TextControl('note', 'Note'),
			amis()->SelectControl('payment_method', 'Payment Method')
				->options(array_map(function($method, $key) {
					return ['label' => $method, 'value' => $key];
				}, Order::PAYMENT_METHOD, array_keys(Order::PAYMENT_METHOD))),
			amis()->SelectControl('status', 'Order Status')
				->options(array_map(function($status, $key) {
					return ['label' => $status, 'value' => $key];
				}, Order::ORDER_STATUS, array_keys(Order::ORDER_STATUS))),
			amis()->DateTimeControl('order_date', 'Order Date')
				->format('YYYY-MM-DD HH:mm:ss'),
			// amis()->DateTimeControl('delivery_date', '發貨時間')
			// 	->format('YYYY-MM-DD HH:mm:ss'),
		]);
	}

	public function detail()
	{
		return $this->baseDetail()->body([
			amis()->TextControl('id', 'ID')->static(),
			amis()->TextControl('user_id', 'User ID')->static(),
			amis()->NumberControl('total_price', 'Total Price')
				->precision(2)
				->prefix('$')
				->static(),
			amis()->TableControl('product_list', 'Product List')
				->source('${product_list}')
				->columns([
					amis()->TableColumn('title', 'Product Name'),
					amis()->TableColumn('quantity', 'Quantity'),
					amis()->TableColumn('price', 'Price')
						->type('number')
						->precision(2)
						->prefix('$')
						,
					amis()->TableColumn('uuid', 'Order ID'),
				])
				->static(),
			amis()->TextControl('address', 'Delivery Address')->static(),
			amis()->TextControl('phone', 'Phone')->static(),
			amis()->TextControl('email', 'Email')->static(),
			amis()->TextControl('note', 'Note')->static(),
			amis()->TextControl('payment_method', 'Payment Method')
				->static()
				->value(function ($value) {
					return Order::PAYMENT_METHOD[$value] ?? $value;
				}),
			amis()->TextControl('status', 'Order Status')
				->static()
				->value(function ($value) {
					return Order::ORDER_STATUS[$value] ?? $value;
				}),
			amis()->TextControl('order_date', 'Order Date')
				->static(),
			// amis()->TextControl('delivery_date', '發貨時間')
			// 	->static(),
			amis()->TextControl('created_at', 'Created Time')->static(),
			amis()->TextControl('updated_at', 'Updated Time')->static(),
		]);
	}

	// public function complete($id)
	// {
	// 	$order = $this->service->update($id, ['status' => 4]);
	// 	return true;
	// 	// return ('訂單已完成');
	// }

}
