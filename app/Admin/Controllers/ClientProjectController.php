<?php

namespace App\Admin\Controllers;

use App\Models\Product;
use App\Models\ClientProject;
use App\Services\ClientProjectService;
use Slowlyo\OwlAdmin\Controllers\AdminController;

/**
 * client_projects
 *
 * @property ClientProjectService $service
 */
class ClientProjectController extends AdminController
{
	protected string $serviceName = ClientProjectService::class;

	public function list()
	{
		$crud = $this->baseCRUD()
			->filterTogglable(false)
			->headerToolbar([
				$this->createButton('dialog'),
				...$this->baseHeaderToolBar()
			])
			->columns([
				amis()->TableColumn('id', 'ID')->sortable(),
				// 使用关系获取产品名称
				amis()->TableColumn('product.title', 'Product'),
				amis()->TableColumn('user_id', 'UserId'),
				// create a button click local_uuid to other page
				amis()->VanillaAction()->label('Preview Link')->onEvent([
					'click' => [
						'actions' => [
							[
								'ignoreError' => '',
								'script' => '
									const data = event.data;
									const APP_URL = "' . config('app.url') . '";
									console.log(data);
									if(data){
										window.open(APP_URL + "/client-projects/preview/"+ data.id + "/" + data.local_uuid);
									}
									else{
										alert("user not submit form yet!");
									}
								',
								'actionType' => 'custom',
							],
						],
					],
				]),

				amis()->TableColumn('local_uuid', 'LocalUuid'),
				amis()->TableColumn('status', 'Status')
					->type('mapping')
					->map(ClientProject::CLIENT_PROJECT_STATUS),
				amis()->TableColumn('created_at', admin_trans('admin.created_at'))->type('datetime')->sortable(),
				amis()->TableColumn('updated_at', admin_trans('admin.updated_at'))->type('datetime')->sortable(),
				$this->rowActions('dialog')
			]);

		return $this->baseList($crud->with(['product']));
	}

	public function form($isEdit = false)
	{
		return $this->baseForm()->body([
			// amis()->TextControl('product_template_id', 'ProductTemplateId'),
			amis()->TextControl('product.title', 'Product'),
			amis()->TextControl('user_id', 'UserId'),
			amis()->TextControl('local_uuid', 'LocalUuid'),
			// amis()->TextControl('saved', 'Saved'),
			// amis()->TextControl('name', 'Name'),
			// amis()->TextControl('description', 'Description'),
			// amis()->TextControl('url', 'Url'),
			amis()->SelectControl('status', 'Status')
					->options(ClientProject::CLIENT_PROJECT_STATUS),
			// amis()->TextControl('project_data', 'ProjectData'),
		])->with([
			'product' => function ($query) {
				return $query->select('id', 'title');
			},
		]);
	}

	public function detail()
	{
		return $this->baseDetail()->body([
			amis()->TextControl('id', 'ID')->static(),
			// amis()->TextControl('product_template_id', 'ProductTemplateId')->static(),
			amis()->TextControl('product.title', 'Product')->static(),
			amis()->TextControl('user_id', 'UserId')->static(),
			amis()->TextControl('local_uuid', 'LocalUuid')->static(),
			// amis()->TextControl('saved', 'Saved')->static(),
			// amis()->TextControl('name', 'Name')->static(),
			// amis()->TextControl('description', 'Description')->static(),
			// amis()->TextControl('url', 'Url')->static(),
			// amis()->TextControl('project_data', 'ProjectData')->static(),
			amis()->TextControl('created_at', admin_trans('admin.created_at'))->static(),
			amis()->TextControl('updated_at', admin_trans('admin.updated_at'))->static(),
		]);
	}
}