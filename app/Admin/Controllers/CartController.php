<?php

namespace App\Admin\Controllers;

use App\Services\CartService;
use Slowlyo\OwlAdmin\Controllers\AdminController;

/**
 * Cart
 *
 * @property CartService $service
 */
class CartController extends AdminController
{
	protected string $serviceName = CartService::class;

	public function list()
	{
		$crud = $this->baseCRUD()
			->filterTogglable(false)
			->headerToolbar([
				$this->createButton('dialog'),
				...$this->baseHeaderToolBar()
			])
			->columns([
				amis()->TableColumn('id', 'ID')->sortable(),
				amis()->TableColumn('uuid', 'Uuid'),
				amis()->TableColumn('user_id', 'UserId'),
				amis()->TableColumn('client_project_id', 'ClientProjectId'),
				amis()->TableColumn('qty', 'Qty')->sortable(),
				amis()->TableColumn('cart_meta', 'CartMeta'),
				amis()->TableColumn('created_at', admin_trans('admin.created_at'))->type('datetime')->sortable(),
				amis()->TableColumn('updated_at', admin_trans('admin.updated_at'))->type('datetime')->sortable(),
				$this->rowActions('dialog')
			]);

		return $this->baseList($crud);
	}

	public function form($isEdit = false)
	{
		return $this->baseForm()->body([
			amis()->TextControl('uuid', 'Uuid'),
			amis()->TextControl('user_id', 'UserId'),
			amis()->TextControl('client_project_id', 'ClientProjectId'),
			amis()->TextControl('qty', 'Qty'),
			amis()->TextControl('cart_meta', 'CartMeta'),
		]);
	}

	public function detail()
	{
		return $this->baseDetail()->body([
			amis()->TextControl('id', 'ID')->static(),
			amis()->TextControl('uuid', 'Uuid')->static(),
			amis()->TextControl('user_id', 'UserId')->static(),
			amis()->TextControl('client_project_id', 'ClientProjectId')->static(),
			amis()->TextControl('qty', 'Qty')->static(),
			amis()->TextControl('cart_meta', 'CartMeta')->static(),
			amis()->TextControl('created_at', admin_trans('admin.created_at'))->static(),
			amis()->TextControl('updated_at', admin_trans('admin.updated_at'))->static(),
		]);
	}
}