<?php

namespace App\Admin\Controllers;

use App\Models\ClientInvoice;
use App\Services\ClientInvoiceService;
use Slowlyo\OwlAdmin\Controllers\AdminController;

/**
 * Client Invoice
 *
 * @property ClientInvoiceService $service
 */
class ClientInvoiceController extends AdminController
{
	protected string $serviceName = ClientInvoiceService::class;

	public function list()
	{
		$crud = $this->baseCRUD()
			->filterTogglable(false)
			->headerToolbar([
				$this->createButton('dialog'),
				...$this->baseHeaderToolBar()
			])
			->columns([
				amis()->TableColumn('id', 'ID')->sortable(),
				amis()->TableColumn('user_id', 'UserId'),
				// amis()->TableColumn('local_uuid', 'LocalUuid'),
				amis()->TableColumn('uuid', 'Uuid'),
				// object to json
				amis()->TableColumn('delivery_detail.phone', 'Phone'),
				amis()->TableColumn('delivery_detail.email', 'Address'),
				// amis()->TableColumn('cart', 'Cart'),
				// amis()->TableColumn('coupon_code', 'CouponCode'),
				// amis()->TableColumn('currency', 'Currency'),
				// amis()->TableColumn('subtotal', 'Subtotal'),
				// amis()->TableColumn('discount', 'Discount'),
				amis()->TableColumn('shipping', 'Shipping'),
				amis()->TableColumn('total', 'Total'),
				amis()->TableColumn('payment_method', 'PaymentMethod')
					->type('mapping')
					->map(ClientInvoice::PAYMENT_METHOD),
				// amis()->TableColumn('paid_at', 'PaidAt'),
				amis()->TableColumn('created_at', admin_trans('admin.created_at'))->type('datetime')->sortable(),
				amis()->TableColumn('updated_at', admin_trans('admin.updated_at'))->type('datetime')->sortable(),
				$this->rowActions('dialog')
			]);

		return $this->baseList($crud);
	}

	public function form($isEdit = false)
	{
		return $this->baseForm()->body([
			amis()->TextControl('user_id', 'UserId'),
			amis()->TextControl('local_uuid', 'LocalUuid'),
			amis()->TextControl('uuid', 'Uuid'),
			amis()->TextControl('delivery_detail', 'DeliveryDetail'),
			amis()->TextControl('cart', 'Cart'),
			amis()->TextControl('coupon_code', 'CouponCode'),
			amis()->TextControl('currency', 'Currency'),
			amis()->TextControl('subtotal', 'Subtotal'),
			amis()->TextControl('discount', 'Discount'),
			amis()->TextControl('shipping', 'Shipping'),
			amis()->TextControl('total', 'Total'),
			amis()->TextControl('payment_method', 'PaymentMethod'),
			amis()->TextControl('status', 'Status'),
			amis()->TextControl('paid_at', 'PaidAt'),
		]);
	}

	public function detail()
	{
		return $this->baseDetail()->body([
			amis()->TextControl('id', 'ID')->static(),
			amis()->TextControl('user_id', 'UserId')->static(),
			amis()->TextControl('local_uuid', 'LocalUuid')->static(),
			amis()->TextControl('uuid', 'Uuid')->static(),
			amis()->TextControl('delivery_detail', 'DeliveryDetail')->static(),
			amis()->TextControl('cart', 'Cart')->static(),
			amis()->TextControl('coupon_code', 'CouponCode')->static(),
			amis()->TextControl('currency', 'Currency')->static(),
			amis()->TextControl('subtotal', 'Subtotal')->static(),
			amis()->TextControl('discount', 'Discount')->static(),
			amis()->TextControl('shipping', 'Shipping')->static(),
			amis()->TextControl('total', 'Total')->static(),
			amis()->TextControl('payment_method', 'PaymentMethod')->static(),
			amis()->TextControl('status', 'Status')->static(),
			amis()->TextControl('paid_at', 'PaidAt')->static(),
			amis()->TextControl('created_at', admin_trans('admin.created_at'))->static(),
			amis()->TextControl('updated_at', admin_trans('admin.updated_at'))->static(),
		]);
	}
}