<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use App\Admin\Controllers\OrderController;

Route::get('/admin', fn() => \Slowlyo\OwlAdmin\Admin::view());

Route::group([
    'domain'     => config('admin.route.domain'),
    'prefix'     => config('admin.route.prefix'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {
    $router->resource('dashboard', \App\Admin\Controllers\HomeController::class);
    $router->resource('system/settings', \App\Admin\Controllers\SettingController::class);
    $router->resource('products', \App\Admin\Controllers\ProductController::class);
    $router->resource('orders', \App\Admin\Controllers\OrderController::class);
    $router->resource('client_invoices', \App\Admin\Controllers\ClientInvoiceController::class);
    

    
    // 添加上传路由
    $router->post('upload/image/{folder}', [\App\Admin\Controllers\UploadController::class, 'adminUploadImage']);

    $router->post('orders/{id}/complete', [OrderController::class, 'complete']);
});
