# dsp_website


```
{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "deploy AY73TzrfreUWG9P",
            "type": "shell",
            "command": "rsync -av -e \"ssh -p 444\" ./public/build <EMAIL>:/www/wwwroot/demo.dspworkshop.com/public",
            "args": [
                // Ask msbuild to generate full paths for file names.
                // "-av -e \"ssh -p 444\" ./public/build <EMAIL>:/www/wwwroot/demo.dspworkshop.com/public",
            ],
            "group": "build",
            "presentation": {
                // Reveal the output only if unrecognized errors occur.
                "reveal": "silent"
            },
            // Use the standard MS compiler pattern to detect errors, warnings and infos
            // "problemMatcher": "$msCompile"
        }
    ]
}

```
