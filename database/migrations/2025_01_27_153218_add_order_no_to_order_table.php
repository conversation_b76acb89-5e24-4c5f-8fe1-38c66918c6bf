<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            //
            $table->string('order_no')->nullable()->after('id');
            $table->string('discount')->nullable()->after('total_price');
            $table->string('coupon_code')->nullable()->after('discount');
            $table->double('shipping')->nullable()->after('coupon_code');
            $table->dateTime('paid_date')->nullable()->after('delivery_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('order_no');
            $table->dropColumn('discount');
            $table->dropColumn('coupon_code');
            $table->dropColumn('shipping');
            $table->dropColumn('paid_date');
        });
    }
};
