<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('client_invoices', function (Blueprint $table) {
            $table->comment('Client Invoice');
            $table->increments('id');
            $table->unsignedInteger('user_id')->nullable();
            $table->string('local_uuid')->nullable();
            $table->string('uuid')->nullable();
            $table->json('delivery_detail')->nullable();
            $table->json('cart')->nullable();
            $table->string('coupon_code')->nullable();
            $table->string('currency')->nullable();
            $table->double('subtotal')->nullable();
            $table->double('discount')->nullable();
            $table->double('shipping')->nullable();
            $table->double('total')->nullable();
            $table->string('payment_method')->nullable();
            $table->string('status')->nullable();
            $table->dateTime('paid_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('client_invoices');
    }
};
