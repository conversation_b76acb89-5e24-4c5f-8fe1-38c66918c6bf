<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            //
            $table->string('delivery_method')->nullable();
            $table->json('payment_meta')->nullable();
        });
        Schema::table('client_invoices', function (Blueprint $table) {
            //
            $table->string('delivery_method')->nullable();
            $table->json('payment_meta')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            //
            $table->dropColumn('delivery_method');
            $table->dropColumn('payment_meta');
        });
        Schema::table('client_invoices', function (Blueprint $table) {
            //
            $table->dropColumn('delivery_method');
            $table->dropColumn('payment_meta');
        });
    }
};
