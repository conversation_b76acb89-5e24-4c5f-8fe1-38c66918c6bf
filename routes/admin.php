<?php

// =====================================================================

// !!  路由文件由 owl-admin 自动生成，请不要手动修改。

// !!  Route file auto generated by owl-admin, don't modify it manually.

// =====================================================================

use Slowlyo\OwlAdmin\Admin;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'domain'     => Admin::config('admin.route.domain'),
    'prefix'     => Admin::config('admin.route.prefix'),
    'middleware' => Admin::config('admin.route.middleware'),
], function (Router $router) {
    // products
    $router->resource('products', \App\Admin\Controllers\ProductController::class);
    // orders
    $router->resource('orders', \App\Admin\Controllers\OrderController::class);
    // client_projects
    $router->resource('client_projects', \App\Admin\Controllers\ClientProjectController::class);
    // Client Invoice
    $router->resource('client_invoice', \App\Admin\Controllers\ClientInvoiceController::class);
    // Cart
    $router->resource('carts', \App\Admin\Controllers\CartController::class);

});