<?php

use App\Models\User;
use Inertia\Inertia;
use App\Models\Order;
use App\Models\Product;
use Illuminate\Http\Request;
use App\Models\ClientInvoice;
use App\Models\ClientProject;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\UserController;
use Laravel\Socialite\Facades\Socialite;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\StripeController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ClientProjectController;

// Socialite
Route::get('/auth/{socialmedia}/redirect', function ( $socialmedia  ) {
    // dd(Socialite::driver($socialmedia));
    return Socialite::driver($socialmedia)
    ->redirect();
});
Route::get('/auth/{socialmedia}/callback', function ($socialmedia) {

    // dump(session()->all());
    // dd(request()->all());
    $socialUser = Socialite::driver($socialmedia)->user();
    // dd($socialmedia);
    // check user is exists
    $checkUser = User::where('email', $socialUser->email)->exists();
    if(!$checkUser) {
        $user = User::create([
            'name' => $socialUser->name,
            'email' => $socialUser->email,
            'avatar' => $socialUser->avatar,
            'password' => $socialUser->email,
            'socialmedia' => $socialmedia,
            'socialmedia_id' => $socialUser->id,
            'token' => $socialUser->token,
            'refreshToken' => $socialUser->refreshToken,
            'expiresIn' => $socialUser->expiresIn,
            'email_verified_at' => now(),
        ]);
    }
    else{
        // dd($socialUser->token);
        $user = User::where('email', $socialUser->email)->first();
        $user->update([
            'token' => $socialUser->token,
            'socialmedia_id' => $socialUser->id,
            'socialmedia' => $socialmedia,
        ]);
    }
    Auth::login($user, true);
    return redirect('/user/profile');
});

Route::get('/', function () {
    return Inertia::render('Index', [
    ]);
});

Route::get('/cart', function () {
    if(!Auth::check()) {
        return redirect('/user/login');
    }
    if(request('order')) {
        $order = Order::where('order_no', request('order'))->first();
        // dd($order);
        return Inertia::render('Cart', [
            'order' => $order,
        ]);
    }
    return Inertia::render('Cart', [
    ]);
});

Route::get('/thankyou/{invoice:uuid}', function ( ClientInvoice $invoice ) {
    if(!Auth::check()) {
        return redirect('/user/login');
    }
    return Inertia::render('ThankYou', [
        'invoice' => $invoice->only([
            'uuid',
            'cart',
            'currency',
            'subtotal',
            'discount',
            'shipping',
            'total',
            'payment_method',
            'status',
            'paid_at',
            'created_at',
            'updated_at',
            'order',
        ]),
    ]);

});

Route::get('/user-order-info/{status}', function ($status) {
    if(!Auth::check()) {
        return redirect('/user/login');
    }
    if($status == 'waiting-for-payment') {
        $waitingForPaymentOrders = Order::where('user_id', Auth::user()->id)
            ->where('status', 1)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($order) {
                return [
                    'id' => $order->id,
                    'order_no' => $order->order_no,
                    'order_date' => $order->order_date,
                    'total_price' => $order->total_price,
                    'product_list' => $order->product_list,
                    'status' => $order->status,
                    'payment_method' => $order->payment_method,
                    'transition_id' => $order->transition_id
                ];
            });
            return Inertia::render('UserOrderInfo', [
                'status' => 'waiting-for-payment',
                'title' => '等待付款訂單',
                'orders' => $waitingForPaymentOrders,
            ]);
    }
    if($status == 'waiting-for-delivery') {
        $waitingForPaymentOrders = Order::where('user_id', Auth::user()->id)
        ->where('status', 2)
        ->orderBy('created_at', 'desc')
        ->get()
        ->map(function ($order) {
            return [
                'id' => $order->id,
                'order_no' => $order->order_no,
                'order_date' => $order->order_date,
                'total_price' => $order->total_price,
                'product_list' => $order->product_list,
                'status' => $order->status,
                'payment_method' => $order->payment_method,
                'transition_id' => $order->transition_id,
                'invoice' => $order->invoice,
            ];
        });
        return Inertia::render('UserOrderInfo', [
            'status' => 'waiting-for-delivery',
            'title' => '等待收貨訂單',
            'orders' => $waitingForPaymentOrders,
        ]);
    }
    if($status == 'payment-history') {
        $paymentHistory = Order::where('user_id', Auth::user()->id)
            ->whereIn('status', [4, 5])
            ->orderBy('created_at', 'desc')->with(['invoice'])
            ->get();
        return Inertia::render('UserOrderInfo', [
            'status' => 'payment-history',
            'title' => '過往紀錄',
            'orders' => $paymentHistory,
        ]);
    }
})->name('waiting-for-payment');

Route::get('/product', function () {
    return Inertia::render('Product', [
    ]);
})->name('product');

Route::get('/product/{id}', function ($id) {
    return Inertia::render('Product', [
        'id' => $id,
    ]);
});

Route::post('/upload', function (Request $request) {
    if ($request->hasFile('file')) {
        $path = $request->file('file')->store('red_packet');
        return response()->json(['path' => $path], 200);
    }
    return response()->json(['error' => 'No file uploaded'], 400);
});


Route::prefix('product')->middleware('auth')->group(function () {
    Route::get('/{id}/edit', function ($id) {
        $clientProject = null;
        if (request('uuid')) {
        $clientProject = ClientProject::where('local_uuid', request('uuid'))
            ->where('user_id', auth()->id())
            ->first();
    }
    // 获取产品数据
    $product = Product::findOrFail($id);
    // 准备项目数据
    $projectData = $clientProject ? [
        'id' => $clientProject->id,
        'product_template_id' => $clientProject->product_template_id,
        'project_data' => $clientProject->project_data,
        'local_uuid' => $clientProject->local_uuid,
    ] : null;
    if($id == 1) {
    return Inertia::render('ProductRedPocketEdit', [
        'product' => [
            'id' => $product->id,
            'title' => $product->title,
            'price' => $product->price,
            'images' => $product->images,
            'specs' => $product->specs,
            // 添加其他需要的产品字段
        ],
            'ClientProject' => $projectData
        ]);
    }
    });
});

Route::group(['prefix' => 'user'], function () {
    Route::middleware(['auth'])->group(function () {
        Route::get('/profile', function () {
            if(!Auth::check()) {
                return redirect('/user/login');
            }
            return Inertia::render('Profile');
        })->name('profile');

        Route::post('/avatar', [AuthController::class, 'updateAvatar']);

        Route::get('/logout', [AuthController::class, 'logout'])
        ->name('logout');

        // 地址
        Route::post('/address', [AuthController::class, 'updateAddress']);
        Route::delete('/address/{id}', [AuthController::class, 'deleteAddress']);
        Route::post('/address/set-default/{id}', [AuthController::class, 'setDefaultAddress']);

        // Profile
        Route::post('/profile/update', [AuthController::class, 'updateProfile']);
        Route::post('/password/update', [AuthController::class, 'updatePassword']);
        Route::post('/delivery-info', [UserController::class, 'updateDeliveryInfo']);
        Route::get('/get-addresses', [UserController::class, 'getAddresses']);
    });

    Route::get('/login', function () {
        if(Auth::check()) {
            return redirect('/user/profile');
        }
        return Inertia::render('Components/UserPage/LoginPage');
    })->name('loginForm');

    Route::get('/register', function () {
        if(Auth::check()) {
            return redirect('/user/profile');
        }
        return Inertia::render('Components/UserPage/SigninPage');
    })->name('registerForm');

    Route::post('/login', [AuthController::class, 'login'])->name('login');
    Route::post('/register', [AuthController::class, 'register'])->name('register');
});


Route::get('/PacketDesign/{filename}', function ($filename) {
    $path = public_path("PacketDesign/{$filename}");
    if (!file_exists($path)) {
        abort(404);
    }
    return response()->file($path);
})->where('filename', '.*');

Route::get('/api/products', [ProductController::class, 'index']);

Route::middleware(['auth'])->group(function () {
    // Route::post('/create-payment-intent', [OrderController::class, 'createPaymentIntent']);
    Route::post('/create-order', [OrderController::class, 'createOrder']);
    Route::post('/checkout', [StripeController::class, 'checkout']);
    Route::post('/confirm-payment', [StripeController::class, 'confirm']);
});

Route::group(['prefix' => 'orders'], function () {
    Route::post('/confirm-delivery/{id}', [OrderController::class, 'confirmDelivery']);
});

Route::prefix('client-projects')->middleware('auth')->group(function () {
    Route::post('/', [ClientProjectController::class, 'store']);
    // preview
    Route::get('/preview/{id}/{uuid}', function ($id, $uuid) {
        $clientProject = ClientProject::with(['user'])->where('local_uuid', $uuid)->first();
        return Inertia::render('Components/Product/RedPocketPreview', [
            'clientProject' => $clientProject,
            'auth' => [
                'user' => Auth::user()
            ]
        ]);
    });
});

Route::prefix('cart')->middleware('auth')->group(function () {
    Route::post('/add', [CartController::class, 'add']);
    Route::get('/list', [CartController::class, 'list']);
    Route::post('/update', [CartController::class, 'update']);
    Route::delete('/{clientProjectId}', [CartController::class, 'destroy']);
});

Route::get('/api/cart/count', [CartController::class, 'getCount']);


// Rainbox
Route::prefix('rainbow')->group(function () {
    Route::get('/profile', function () {
        return Inertia::render('RainbowProfile');
    });
    Route::get('/showcase', function () {
        return Inertia::render('RainbowShowcase');
    });
    Route::get('/red-pocket', function () {
        return Inertia::render('RainbowRedPocket', [
        ]);
    });
});

// Vcard
Route::prefix('vcard')->group(function () {
    Route::get('/', function () {
        return Inertia::render('Vcard');
    });
});
