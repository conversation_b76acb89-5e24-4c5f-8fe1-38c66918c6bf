<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],
    'facebook' => [
        'client_id' => '460476322803808',
        'client_secret' => '1842e21da7dd29ff07f930f8f52d5132',
        'redirect' => env('APP_URL').'/auth/facebook/callback',
    ],
    'google' => [
        'client_id' => '518371311493-r50i4hugbjekkf42m02k8vvnmq0q9g5p.apps.googleusercontent.com',
        'client_secret' => '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',
        'redirect' => env('APP_URL').'/auth/google/callback',
    ],
    'instagram' => [
        'client_id' => '902065564631106',
        'client_secret' => 'ba270279d2097a1bf2a13298d0dd42a7',
        'redirect' => env('APP_URL').'/auth/instagram/callback',
    ],
    'instagrambasic' => [
        'client_id' => '902065564631106',
        'client_secret' => 'ba270279d2097a1bf2a13298d0dd42a7',
        'redirect' => env('APP_URL').'/auth/instagrambasic/callback',
    ],

];
// 'client_id' => '415537003916-rehp82pg97sb7kssqp9cv2l10ln54n72.apps.googleusercontent.com',
// 'client_secret' => '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',