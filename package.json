{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "deploy": "rsync -av -e \"ssh -p 444\" ./public/build <EMAIL>:/www/wwwroot/demo.dspworkshop.com/public"}, "devDependencies": {"@headlessui/react": "^2.0.0", "@inertiajs/react": "^1.0.0", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.12", "axios": "^1.7.4", "laravel-vite-plugin": "^1.0.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.4.14", "vite": "^5.0.0"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@pdf-lib/fontkit": "^1.1.1", "@radix-ui/react-avatar": "^1.1.1", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.5.0", "antd": "^5.21.6", "api": "^6.1.2", "fabric": "^6.5.3", "framer-motion": "^11.11.11", "gsap": "^3.12.5", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "lodash": "^4.17.21", "pdf-lib": "^1.17.1", "react-marquee-slider": "^1.1.5", "react-router-dom": "^6.27.0", "styled-components": "^6.1.15", "swiper": "^11.1.14"}}