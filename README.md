# DSP Workshop 电商网站

一个基于Laravel + React + Inertia.js的现代化电商平台，专注于红包产品的销售和定制服务。

## 🚀 项目概述

DSP Workshop是一个专业的红包定制电商网站，提供个性化红包设计、在线编辑、购物车管理、支付处理等完整的电商功能。项目采用前后端分离架构，后端使用Laravel框架，前端使用React + Inertia.js，提供流畅的单页应用体验。

## ✨ 主要功能

### 🛍️ 电商核心功能
- **产品展示**: 展示各类红包产品，支持图片、规格、价格等信息
- **产品定制**: 在线红包设计编辑器，支持个性化定制
- **购物车管理**: 添加、删除、更新购物车商品
- **订单管理**: 完整的订单流程，支持多种支付方式
- **用户系统**: 用户注册、登录、个人资料管理
- **支付集成**: Stripe支付网关集成，支持信用卡支付

### 🎨 红包定制功能
- **在线编辑器**: 基于Fabric.js的画布编辑器
- **模板系统**: 预设红包模板，支持快速定制
- **实时预览**: 实时预览定制效果
- **项目保存**: 保存定制项目，支持后续编辑

### 👤 用户管理
- **社交登录**: 支持Google、Facebook等社交平台登录
- **地址管理**: 多地址管理，支持默认地址设置
- **订单历史**: 查看订单状态和历史记录
- **个人资料**: 头像上传、密码修改等

## 🛠️ 技术栈

### 后端技术
- **PHP 8.2+**: 核心编程语言
- **Laravel 11**: 现代化PHP框架
- **MySQL**: 主数据库
- **Laravel Sanctum**: API认证
- **Laravel Socialite**: 社交登录
- **Stripe**: 支付处理
- **Owl Admin**: 后台管理

### 前端技术
- **React 18**: 用户界面框架
- **Inertia.js**: 无API的SPA解决方案
- **Tailwind CSS**: 实用优先的CSS框架
- **Vite**: 现代化构建工具
- **GSAP**: 高性能动画库
- **Framer Motion**: React动画库

### 开发工具
- **Laravel Sail**: Docker开发环境
- **Pest**: PHP测试框架
- **Laravel Pint**: PHP代码风格检查
- **Vite**: 前端构建和热重载

## 📁 项目结构

```
dsp_website/
├── app/                          # Laravel应用核心
│   ├── Http/Controllers/         # 控制器
│   ├── Models/                   # 数据模型
│   ├── Services/                 # 业务逻辑服务
│   └── Admin/                    # 后台管理
├── resources/                     # 前端资源
│   ├── js/                       # React组件
│   │   ├── Pages/                # 页面组件
│   │   ├── Components/           # 可复用组件
│   │   └── Layouts/              # 布局组件
│   └── css/                      # 样式文件
├── routes/                        # 路由定义
├── database/                      # 数据库迁移和种子
├── config/                        # 配置文件
├── public/                        # 公共资源
└── docker/                        # Docker配置
```

## 🚀 快速开始

### 环境要求
- PHP 8.2+
- Composer 2.0+
- Node.js 18+
- Docker (推荐使用Laravel Sail)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd dsp_website
```

2. **安装PHP依赖**
```bash
composer install
```

3. **安装前端依赖**
```bash
npm install
# 或使用pnpm
pnpm install
```

4. **环境配置**
```bash
cp .env.example .env
php artisan key:generate
```

5. **数据库配置**
```bash
# 配置.env文件中的数据库连接信息
php artisan migrate
php artisan db:seed
```

6. **启动开发服务器**
```bash
# 使用Laravel Sail (推荐)
./vendor/bin/sail up

# 或分别启动
php artisan serve
npm run dev
```

### 使用Docker (推荐)

项目包含完整的Docker配置，使用Laravel Sail可以快速启动开发环境：

```bash
# 启动所有服务
./vendor/bin/sail up -d

# 查看服务状态
./vendor/bin/sail ps

# 执行Artisan命令
./vendor/bin/sail artisan migrate

# 执行npm命令
./vendor/bin/sail npm run dev
```

## 🔧 配置说明

### 环境变量
在`.env`文件中配置以下关键变量：

```env
# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=dsp_website
DB_USERNAME=root
DB_PASSWORD=

# Stripe支付配置
VITE_STRIPE_PUBLIC_KEY=your_stripe_public_key
STRIPE_SECRET_KEY=your_stripe_secret_key

# 应用配置
APP_NAME="DSP Workshop"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost
```

### 支付配置
项目集成了Stripe支付系统，需要在Stripe控制台获取API密钥并配置到环境变量中。

## 📱 主要页面

- **首页** (`/`): 产品展示、分类导航、热门推荐
- **产品页** (`/product`): 产品列表和详情
- **定制编辑器** (`/product/{id}/edit`): 红包在线定制
- **购物车** (`/cart`): 购物车管理和结算
- **用户中心** (`/user/profile`): 个人资料、订单管理
- **支付确认** (`/thankyou/{uuid}`): 支付成功页面

## 🔌 API接口

### 产品相关
- `GET /api/products` - 获取产品列表
- `GET /api/products/{id}` - 获取产品详情

### 购物车相关
- `POST /cart/add` - 添加商品到购物车
- `GET /cart/list` - 获取购物车列表
- `POST /cart/update` - 更新购物车商品数量
- `DELETE /cart/{id}` - 删除购物车商品

### 订单相关
- `POST /checkout` - 创建支付意图
- `POST /confirm-payment` - 确认支付

## 🧪 测试

项目使用Pest测试框架：

```bash
# 运行所有测试
./vendor/bin/sail test

# 运行特定测试
./vendor/bin/sail test --filter=ProductTest
```

## 🚀 部署

### 生产环境部署
项目包含部署脚本，支持rsync部署到远程服务器：

```bash
# 构建前端资源
npm run build

# 部署到服务器
npm run deploy
```

### 部署任务配置
部署配置位于`DEPLOY_TASKS.md`文件中，包含VSCode任务配置。

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者: DSP Workshop团队
- 邮箱: [联系邮箱]
- 项目链接: [项目地址]

## 🙏 致谢

感谢以下开源项目和技术社区的支持：
- Laravel框架及其生态系统
- React和Inertia.js社区
- Tailwind CSS团队
- Stripe支付平台

---

**注意**: 这是一个活跃开发中的项目，功能可能会持续更新和改进。如有问题或建议，请通过Issues页面反馈。
